<?php

namespace app\modules\api\services\manufacter;

use Yii;
use app\common\models\ActionLogger;
use app\common\models\MaterialProduction;
use app\common\models\Product;
use app\common\models\ProductDefect;
use app\common\models\ProductIngredients;
use app\common\models\ProductStorage;
use app\common\models\ProductStorageHistory;
use app\common\models\ProductStorageHistoryMaterials;
use yii\base\Component;

/**
 * Сервис для удаления выпущенной продукции
 */
class DeleteReleaseFinishedProductService extends Component
{
    /**
     * Удаление выпущенной продукции
     *
     * @param int $productStorageId ID записи о выпуске продукции
     * @return array Результат операции
     * @throws \Exception
     */
    public function deleteReleaseFinishedProduct($productStorageId)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $productStorage = $this->getProductStorage($productStorageId);

            // Восстанавливаем ранее использованные продукты для переупаковки
            $usedRepackagingProducts = $this->getUsedRepackagingProducts($productStorage->product_id);
            $this->restoreUsedRepackagingProducts($usedRepackagingProducts);

            // Получаем доступные продукты для переупаковки
            $repackagingProducts = $this->getRepackagingProducts($productStorage->product_id);
            $repackagingQuantity = (float)$this->calculateRepackagingQuantity($repackagingProducts);

            // Вычисляем количество материалов для возврата
            $returnQuantity = $this->calculateReturnQuantity($productStorage, $repackagingQuantity);

            // Возвращаем материалы только для новых продуктов
            $epsilon = 0.0001;
            if ($returnQuantity > $epsilon) {
                // Сначала проверяем историю использованных материалов
                $materialsReturned = $this->returnMaterialsFromHistory($productStorage);

                // Если материалы не были возвращены из истории, используем метод на основе ингредиентов
                if (!$materialsReturned) {
                    $this->returnMaterialsToProduction($productStorage->product_id, $returnQuantity);
                }
            }

            // Сначала удаляем связанные записи материалов из истории
            $this->deleteProductStorageHistoryMaterials($productStorage);

            // Затем удаляем записи истории
            $this->deleteProductStorageHistory($productStorage);

            // Помечаем запись о выпуске продукции как удаленную
            $this->markProductStorageAsDeleted($productStorage);

            // Логируем удаление выпуска продукции
            $this->logDeleteReleaseFinishedProduct($productStorage, $productStorageId, $repackagingQuantity, $returnQuantity, $repackagingProducts);

            $transaction->commit();
            return [
                'success' => true,
                'message' => 'Запись успешно удалена'
            ];
        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'success' => false,
                'message' => 'Ошибка при удалении записи',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Получение записи о выпуске продукции
     *
     * @param int $productStorageId ID записи о выпуске продукции
     * @return ProductStorage
     * @throws \Exception
     */
    protected function getProductStorage($productStorageId)
    {
        $productStorage = ProductStorage::findOne($productStorageId);
        if (!$productStorage) {
            throw new \Exception('record_not_found');
        }

        // Проверяем, не подтверждена ли запись
        if ($productStorage->accepted_at !== null) {
            throw new \Exception('dont_update_accepted_record');
        }

        // Проверяем, не удалена ли запись
        if ($productStorage->deleted_at !== null) {
            throw new \Exception('dont_update_deleted_record');
        }

        return $productStorage;
    }

    /**
     * Получение использованных продуктов для переупаковки
     *
     * @param int $productId ID продукта
     * @return array Использованные продукты для переупаковки
     */
    protected function getUsedRepackagingProducts($productId)
    {
        return ProductDefect::find()
            ->where([
                'product_id' => $productId,
                'is_repackaging' => true,
            ])
            ->andWhere(['IS NOT', 'deleted_at', null])
            ->andWhere(['IS NOT', 'accepted_at', null])
            ->andWhere(['IS NOT', 'accepted_user_id', null])
            ->all();
    }

    /**
     * Восстановление использованных продуктов для переупаковки
     *
     * @param array $usedRepackagingProducts Использованные продукты для переупаковки
     * @throws \Exception
     */
    protected function restoreUsedRepackagingProducts($usedRepackagingProducts)
    {
        foreach ($usedRepackagingProducts as $usedProduct) {
            $usedProduct->deleted_at = null;
            if (!$usedProduct->save()) {
                throw new \Exception('Ошибка при восстановлении записи о переупаковке');
            }
        }
    }

    /**
     * Получение продуктов для переупаковки
     *
     * @param int $productId ID продукта
     * @return array Продукты для переупаковки
     */
    protected function getRepackagingProducts($productId)
    {
        return ProductDefect::find()
            ->where([
                'product_id' => $productId,
                'is_repackaging' => true,
                'deleted_at' => null
            ])
            ->andWhere(['IS NOT', 'accepted_at', null])
            ->andWhere(['IS NOT', 'accepted_user_id', null])
            ->all();
    }

    /**
     * Расчет количества продуктов для переупаковки
     *
     * @param array $repackagingProducts Продукты для переупаковки
     * @return float Количество продуктов для переупаковки
     */
    protected function calculateRepackagingQuantity($repackagingProducts)
    {
        $repackagingQuantity = 0;
        foreach ($repackagingProducts as $repackagingProduct) {
            $repackagingQuantity += $repackagingProduct->quantity;
        }
        return $repackagingQuantity;
    }

    /**
     * Расчет количества материалов для возврата
     *
     * @param ProductStorage $productStorage Запись о выпуске продукции
     * @param float $repackagingQuantity Количество продуктов для переупаковки
     * @return float Количество материалов для возврата
     */
    protected function calculateReturnQuantity($productStorage, $repackagingQuantity)
    {
        $originalQuantity = (float)$productStorage->quantity;
        $returnQuantity = $originalQuantity;

        if ($repackagingQuantity > 0) {
            // Если есть переупакованные продукты, возвращаем материалы только для новых продуктов
            if ($repackagingQuantity >= $originalQuantity) {
                $returnQuantity = 0; // Явно устанавливаем в 0, а не вычитаем
            } else {
                $returnQuantity = $originalQuantity - $repackagingQuantity;
            }
        }

        return $returnQuantity;
    }

    /**
     * Возврат материалов из истории использования
     *
     * @param ProductStorage $productStorage Запись о выпуске продукции
     * @return bool Были ли возвращены материалы из истории
     * @throws \Exception
     */
    protected function returnMaterialsFromHistory($productStorage)
    {
        // Получаем все записи истории склада для этого product_storage
        $storageHistories = ProductStorageHistory::find()
            ->where(['product_storage_id' => $productStorage->id])
            ->all();

        if (empty($storageHistories)) {
            return false; // Если история не найдена, возвращаем false
        }

        $materialsReturned = false;

        // Для каждой записи истории получаем все использованные материалы
        foreach ($storageHistories as $history) {
            // Получаем материалы из таблицы ProductStorageHistoryMaterials
            $historyMaterials = ProductStorageHistoryMaterials::find()
                ->where(['product_storage_history_id' => $history->id])
                ->all();

            // Если есть данные о списанных материалах, возвращаем их
            if (!empty($historyMaterials)) {
                foreach ($historyMaterials as $material) {
                    $this->returnMaterialToProduction($material->material_id, (float)$material->quantity);
                    $materialsReturned = true;
                }
            } else {
                // Если данных в новой таблице нет (старые записи), проверяем data в ProductStorageHistory
                if (!empty($history->data)) {
                    $historyData = json_decode($history->data, true);

                    // Возвращаем основные материалы
                    if (isset($historyData['materials']) && is_array($historyData['materials'])) {
                        foreach ($historyData['materials'] as $materialId => $quantity) {
                            $this->returnMaterialToProduction($materialId, (float)$quantity);
                            $materialsReturned = true;
                        }
                    }

                    // Возвращаем альтернативные материалы
                    if (isset($historyData['alternative_materials']) && is_array($historyData['alternative_materials'])) {
                        foreach ($historyData['alternative_materials'] as $materialId => $quantity) {
                            $this->returnMaterialToProduction($materialId, (float)$quantity);
                            $materialsReturned = true;
                        }
                    }
                }
            }
        }

        return $materialsReturned;
    }

    /**
     * Возврат материала в производство
     *
     * @param int $materialId ID материала
     * @param float $quantity Количество для возврата
     * @throws \Exception
     */
    protected function returnMaterialToProduction($materialId, $quantity)
    {
        $materialProduction = MaterialProduction::find()
            ->where([
                'material_id' => $materialId,
                'deleted_at' => null,
                'DATE(created_at)' => date('Y-m-d')
            ])
            ->one();

        if (!$materialProduction) {
            $materialProduction = new MaterialProduction();
            $materialProduction->material_id = $materialId;
            $materialProduction->quantity = 0;
            $materialProduction->created_at = date('Y-m-d H:i:s');
        }

        $materialProduction->quantity += $quantity;
        if (!$materialProduction->save()) {
            throw new \Exception('Ошибка при возврате материала в производство');
        }
    }

    /**
     * Возврат материалов в производство на основе ингредиентов продукта
     *
     * @param int $productId ID продукта
     * @param float $returnQuantity Количество материалов для возврата
     * @throws \Exception
     */
    protected function returnMaterialsToProduction($productId, $returnQuantity)
    {
        // ИСПРАВЛЕНИЕ: Получаем информацию о продукте для учета размера блока
        $product = Product::findOne([
            'id' => $productId,
            'deleted_at' => null
        ]);

        if (!$product) {
            return; // Если продукт не найден, нечего возвращать
        }

        $productIngredients = ProductIngredients::find()
            ->where([
                'product_id' => $productId,
                'end_date' => '9999-12-31'
            ])
            ->all();

        if (!empty($productIngredients)) {
            foreach ($productIngredients as $ingredient) {
                // ИСПРАВЛЕНИЕ: Штучный выпуск продукции - соотношение 1:1
                // Возвращаем ровно столько единиц материала, сколько было штук продукции
                $materialQuantity = $returnQuantity;
                $this->returnMaterialToProduction($ingredient->material_id, $materialQuantity);
            }
        }
    }




    /**
     * Удаление связанных записей материалов из истории склада
     *
     * @param ProductStorage $productStorage Запись о выпуске продукции
     */
    protected function deleteProductStorageHistoryMaterials($productStorage)
    {
        $storageHistories = ProductStorageHistory::find()
            ->where(['product_storage_id' => $productStorage->id])
            ->all();

        foreach ($storageHistories as $history) {
            $historyMaterials = ProductStorageHistoryMaterials::find()
                ->where(['product_storage_history_id' => $history->id])
                ->all();

            foreach ($historyMaterials as $material) {
                $material->delete();
            }
        }
    }

    /**
     * Удаление записей истории склада
     *
     * @param ProductStorage $productStorage Запись о выпуске продукции
     */
    protected function deleteProductStorageHistory($productStorage)
    {
        $storageHistories = ProductStorageHistory::find()
            ->where(['product_storage_id' => $productStorage->id])
            ->all();

        foreach ($storageHistories as $history) {
            $history->delete();
        }
    }





    /**
     * Пометка записи о выпуске продукции как удаленной
     *
     * @param ProductStorage $productStorage Запись о выпуске продукции
     * @throws \Exception
     */
    protected function markProductStorageAsDeleted($productStorage)
    {
        $productStorage->deleted_at = date('Y-m-d H:i:s');
        if (!$productStorage->save()) {
            throw new \Exception('Ошибка при пометке записи как удаленной: ' . json_encode($productStorage->getErrors()));
        }
    }







    /**
     * Логирование удаления выпуска продукции
     *
     * @param ProductStorage $productStorage Запись о выпуске продукции
     * @param int $productStorageId ID записи о выпуске продукции
     * @param float $repackagingQuantity Количество продуктов для переупаковки
     * @param float $returnQuantity Количество материалов для возврата
     * @param array $repackagingProducts Продукты для переупаковки
     */
    protected function logDeleteReleaseFinishedProduct($productStorage, $productStorageId, $repackagingQuantity, $returnQuantity, $repackagingProducts)
    {
        $logData = [
            'product_id' => $productStorage->product_id,
            'quantity' => $productStorage->quantity,
            'action' => 'delete'
        ];

        if (isset($repackagingQuantity) && $repackagingQuantity > 0) {
            $logData['repackaging_quantity'] = $repackagingQuantity;
            $logData['return_quantity'] = $returnQuantity;
            $logData['repackaging_product_ids'] = array_map(fn($product) => $product->id, $repackagingProducts);
        }

        // Добавляем информацию о возвращенных материалах
        $epsilon = 0.0001;
        if ($returnQuantity > $epsilon) {
            $logData['materials_returned'] = true;
            $logData['materials_return_quantity'] = $returnQuantity;
        } else {
            $logData['materials_returned'] = false;
        }

        ActionLogger::actionLog(
            'delete_release_finished_product',
            'product_storage',
            $productStorageId,
            $logData
        );
    }
}
