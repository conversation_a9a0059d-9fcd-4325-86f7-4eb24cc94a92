a:14:{s:6:"config";s:4971:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:10:"2.0.52-dev";s:11:"application";a:8:{s:3:"yii";s:10:"2.0.52-dev";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:2:"uz";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:16:{s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/base";s:67:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:62:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:60:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-grid/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:77:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-jui/src";}}s:33:"2amigos/yii2-arrayquery-component";a:3:{s:4:"name";s:33:"2amigos/yii2-arrayquery-component";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@dosamigos/arrayquery";s:75:"D:\OSPanel\domains\silverzavod\vendor/2amigos/yii2-arrayquery-component/src";}}s:17:"yii2mod/yii2-rbac";a:3:{s:4:"name";s:17:"yii2mod/yii2-rbac";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii2mod/rbac";s:55:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-rbac";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.25.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:10:"@yii/faker";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.6.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:23:"3.0.9999999.9999999-dev";s:5:"alias";a:10:{s:10:"@yii/queue";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:71:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:72:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/file";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:76:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:78:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:81:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:68:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.11.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap4/src";}}s:20:"yii2mod/yii2-swagger";a:3:{s:4:"name";s:20:"yii2mod/yii2-swagger";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:16:"@yii2mod/swagger";s:58:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-swagger";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:59644:"a:1:{s:8:"messages";a:51:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.999068;i:4;a:0:{}i:5;i:2611976;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748858865.002224;i:4;a:0:{}i:5;i:2728984;}i:2;a:6:{i:0;s:53:"Bootstrap with app\components\SessionTimeoutComponent";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748858865.003639;i:4;a:0:{}i:5;i:2770192;}i:3;a:6:{i:0;s:22:"Bootstrap with Closure";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748858865.003655;i:4;a:0:{}i:5;i:2770568;}i:4;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1748858865.034867;i:4;a:0:{}i:5;i:3915768;}i:5;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1748858865.054905;i:4;a:0:{}i:5;i:4726024;}i:6;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748858865.064963;i:4;a:0:{}i:5;i:5115688;}i:7;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1748858865.074687;i:4;a:0:{}i:5;i:5580096;}i:8;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748858865.075691;i:4;a:0:{}i:5;i:5607488;}i:20;a:6:{i:0;s:65:"Route requested: 'api/manufacter/update-release-finished-product'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1748858865.080047;i:4;a:0:{}i:5;i:5781176;}i:21;a:6:{i:0;s:19:"Loading module: api";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1748858865.08006;i:4;a:0:{}i:5;i:5782840;}i:22;a:6:{i:0;s:60:"Route to run: api/manufacter/update-release-finished-product";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1748858865.089832;i:4;a:0:{}i:5;i:6143296;}i:23;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1748858865.119529;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7484192;}i:26;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.181641;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8015592;}i:29;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.230503;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8061504;}i:32;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.245127;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8357312;}i:35;a:6:{i:0;s:55:"User '7' logged in from 127.0.0.1. Session not enabled.";i:1;i:4;i:2;s:19:"yii\web\User::login";i:3;d:1748858865.256821;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8643960;}i:36;a:6:{i:0;s:65:"Rate limit skipped: "user" does not implement RateLimitInterface.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:1748858865.256898;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8644552;}i:37;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.260656;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8832136;}i:40;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.266279;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8838760;}i:43;a:6:{i:0;s:27:"Checking role: manufacturer";i:1;i:8;i:2;s:40:"yii\rbac\DbManager::checkAccessRecursive";i:3;d:1748858865.271909;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8842152;}i:44;a:6:{i:0;s:102:"Running action: app\modules\api\controllers\ManufacterController::actionUpdateReleaseFinishedProduct()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1748858865.271966;i:4;a:0:{}i:5;i:8841312;}i:45;a:6:{i:0;s:107:"Failed to set unsafe attribute 'product_storage_id' in 'app\modules\api\models\ReleaseFinishedProductForm'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:1748858865.275099;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:862;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}}i:5;i:8921664;}i:46;a:6:{i:0;s:17:"Begin transaction";i:1;i:8;i:2;s:25:"yii\db\Transaction::begin";i:3;d:1748858865.278185;i:4;a:2:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:33;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9095552;}i:47;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.279647;i:4;a:2:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:35;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9128768;}i:50;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.294267;i:4;a:2:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:35;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9140008;}i:53;a:6:{i:0;s:45:"SELECT * FROM "product_storage" WHERE "id"=76";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.300693;i:4;a:2:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:35;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9146408;}i:56;a:6:{i:0;s:180:"SELECT * FROM "product_defect" WHERE (("product_id"=10) AND ("is_repackaging"=TRUE) AND ("deleted_at" IS NULL)) AND ("accepted_at" IS NOT NULL) AND ("accepted_user_id" IS NOT NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.308575;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:153;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:70;s:8:"function";s:22:"getRepackagingProducts";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9207960;}i:59;a:6:{i:0;s:69:"SELECT * FROM "product_storage_history" WHERE "product_storage_id"=76";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.321167;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:187;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9244448;}i:62;a:6:{i:0;s:2829:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.327472;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:187;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9257936;}i:65;a:6:{i:0;s:891:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.340547;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:187;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9275480;}i:68;a:6:{i:0;s:88:"SELECT * FROM "product_storage_history_materials" WHERE "product_storage_history_id"=334";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.34877;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:200;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9311312;}i:71;a:6:{i:0;s:2839:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_storage_history_materials'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.359952;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:200;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9325320;}i:74;a:6:{i:0;s:901:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_storage_history_materials'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.37435;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:200;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9336032;}i:77;a:6:{i:0;s:122:"SELECT * FROM "material_production" WHERE ("material_id"=1) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-02')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.38139;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:287;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9369720;}i:80;a:6:{i:0;s:2825:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_production'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.386189;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9378192;}i:83;a:6:{i:0;s:887:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_production'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.395019;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9388560;}i:86;a:6:{i:0;s:129:"INSERT INTO "material_production" ("material_id", "quantity", "created_at") VALUES (1, '9', '2025-06-02 15:07:45') RETURNING "id"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.404134;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:297;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9445176;}i:89;a:6:{i:0;s:122:"SELECT * FROM "material_production" WHERE ("material_id"=3) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-02')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.432601;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:287;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9447928;}i:92;a:6:{i:0;s:62:"UPDATE "material_production" SET "quantity"='10' WHERE "id"=30";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1748858865.434674;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:297;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9458792;}i:95;a:6:{i:0;s:123:"SELECT * FROM "material_production" WHERE ("material_id"=12) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-02')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.466761;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:287;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9462176;}i:98;a:6:{i:0;s:130:"INSERT INTO "material_production" ("material_id", "quantity", "created_at") VALUES (12, '9', '2025-06-02 15:07:45') RETURNING "id"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.469249;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:297;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9475480;}i:101;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.471735;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:314;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9508224;}i:104;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.483041;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:314;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9519456;}i:107;a:6:{i:0;s:66:"SELECT * FROM "product" WHERE ("id"=10) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.488837;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:314;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9525416;}i:110;a:6:{i:0;s:291:"SELECT "pi".*, "m"."name" AS "material_name", "m"."unit_type", "m"."category_id" FROM "product_ingredients" "pi" LEFT JOIN "material" "m" ON m.id = pi.material_id WHERE ("pi"."product_id"=10) AND ("pi"."end_date"='9999-12-31') AND ("pi"."is_alternative"=FALSE) AND ("m"."deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.49477;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:333;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9602000;}i:113;a:6:{i:0;s:2825:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_ingredients'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.500573;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:333;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9616200;}i:116;a:6:{i:0;s:887:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_ingredients'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.509616;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:333;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9626464;}i:119;a:6:{i:0;s:290:"SELECT "pi".*, "m"."name" AS "material_name", "m"."unit_type", "m"."category_id" FROM "product_ingredients" "pi" LEFT JOIN "material" "m" ON m.id = pi.material_id WHERE ("pi"."product_id"=10) AND ("pi"."end_date"='9999-12-31') AND ("pi"."is_alternative"=TRUE) AND ("m"."deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.516317;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:350;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9633000;}i:122;a:6:{i:0;s:155:"SELECT * FROM "material_production" WHERE ("material_id" IN (1, 3, 4)) AND ("deleted_at" IS NULL) AND ("quantity" > 0) ORDER BY "material_id", "created_at"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.520464;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:377;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9682712;}i:125;a:6:{i:0;s:21:"Roll back transaction";i:1;i:8;i:2;s:28:"yii\db\Transaction::rollBack";i:3;d:1748858865.522443;i:4;a:2:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:127;s:8:"function";s:8:"rollBack";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9682248;}}}";s:9:"profiling";s:109301:"a:3:{s:6:"memory";i:9882096;s:4:"time";d:0.****************;s:8:"messages";a:64:{i:24;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1748858865.119565;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7485696;}i:25;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1748858865.179015;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7488000;}i:27;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.181765;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8017544;}i:28;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.227739;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8033280;}i:30;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.23057;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8063368;}i:31;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.236356;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8065304;}i:33;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.245182;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8361576;}i:34;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.251036;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8364336;}i:38;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.26072;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8834744;}i:39;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.265153;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8836912;}i:41;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.266322;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8841368;}i:42;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.27066;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8843472;}i:48;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.279718;i:4;a:2:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:35;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9130632;}i:49;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.293109;i:4;a:2:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:35;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9149744;}i:51;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.294322;i:4;a:2:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:35;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9141872;}i:52;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.299861;i:4;a:2:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:35;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9145848;}i:54;a:6:{i:0;s:45:"SELECT * FROM "product_storage" WHERE "id"=76";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.300741;i:4;a:2:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:35;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9149392;}i:55;a:6:{i:0;s:45:"SELECT * FROM "product_storage" WHERE "id"=76";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.305129;i:4;a:2:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:35;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9152400;}i:57;a:6:{i:0;s:180:"SELECT * FROM "product_defect" WHERE (("product_id"=10) AND ("is_repackaging"=TRUE) AND ("deleted_at" IS NULL)) AND ("accepted_at" IS NOT NULL) AND ("accepted_user_id" IS NOT NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.308633;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:153;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:70;s:8:"function";s:22:"getRepackagingProducts";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9211488;}i:58;a:6:{i:0;s:180:"SELECT * FROM "product_defect" WHERE (("product_id"=10) AND ("is_repackaging"=TRUE) AND ("deleted_at" IS NULL)) AND ("accepted_at" IS NOT NULL) AND ("accepted_user_id" IS NOT NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.31914;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:153;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:70;s:8:"function";s:22:"getRepackagingProducts";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9214400;}i:60;a:6:{i:0;s:69:"SELECT * FROM "product_storage_history" WHERE "product_storage_id"=76";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.321214;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:187;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9247840;}i:61;a:6:{i:0;s:69:"SELECT * FROM "product_storage_history" WHERE "product_storage_id"=76";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.326854;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:187;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9251568;}i:63;a:6:{i:0;s:2829:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.327559;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:187;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9260176;}i:64;a:6:{i:0;s:2829:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.339513;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:187;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9278120;}i:66;a:6:{i:0;s:891:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.340602;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:187;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9277720;}i:67;a:6:{i:0;s:891:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.346368;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:187;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9282720;}i:69;a:6:{i:0;s:88:"SELECT * FROM "product_storage_history_materials" WHERE "product_storage_history_id"=334";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.348852;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:200;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9314720;}i:70;a:6:{i:0;s:88:"SELECT * FROM "product_storage_history_materials" WHERE "product_storage_history_id"=334";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.359384;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:200;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9318936;}i:72;a:6:{i:0;s:2839:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_storage_history_materials'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.36003;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:200;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9327560;}i:73;a:6:{i:0;s:2839:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_storage_history_materials'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.372876;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:200;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9342216;}i:75;a:6:{i:0;s:901:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_storage_history_materials'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.374432;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:200;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9338272;}i:76;a:6:{i:0;s:901:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_storage_history_materials'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.379835;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:200;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9342664;}i:78;a:6:{i:0;s:122:"SELECT * FROM "material_production" WHERE ("material_id"=1) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-02')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.381449;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:287;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9372544;}i:79;a:6:{i:0;s:122:"SELECT * FROM "material_production" WHERE ("material_id"=1) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-02')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.385578;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:287;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9374920;}i:81;a:6:{i:0;s:2825:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_production'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.38626;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9380432;}i:82;a:6:{i:0;s:2825:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_production'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.394165;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9393552;}i:84;a:6:{i:0;s:887:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_production'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.395063;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9390800;}i:85;a:6:{i:0;s:887:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_production'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.400007;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9393112;}i:87;a:6:{i:0;s:129:"INSERT INTO "material_production" ("material_id", "quantity", "created_at") VALUES (1, '9', '2025-06-02 15:07:45') RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.404173;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:297;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9446680;}i:88;a:6:{i:0;s:129:"INSERT INTO "material_production" ("material_id", "quantity", "created_at") VALUES (1, '9', '2025-06-02 15:07:45') RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.431147;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:297;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9449120;}i:90;a:6:{i:0;s:122:"SELECT * FROM "material_production" WHERE ("material_id"=3) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-02')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.432653;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:287;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9450752;}i:91;a:6:{i:0;s:122:"SELECT * FROM "material_production" WHERE ("material_id"=3) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-02')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.433641;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:287;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9453632;}i:93;a:6:{i:0;s:62:"UPDATE "material_production" SET "quantity"='10' WHERE "id"=30";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748858865.434717;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:297;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9462192;}i:94;a:6:{i:0;s:62:"UPDATE "material_production" SET "quantity"='10' WHERE "id"=30";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748858865.465928;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:297;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9464200;}i:96;a:6:{i:0;s:123:"SELECT * FROM "material_production" WHERE ("material_id"=12) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-02')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.466839;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:287;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9465000;}i:97;a:6:{i:0;s:123:"SELECT * FROM "material_production" WHERE ("material_id"=12) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-02')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.468124;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:287;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9467408;}i:99;a:6:{i:0;s:130:"INSERT INTO "material_production" ("material_id", "quantity", "created_at") VALUES (12, '9', '2025-06-02 15:07:45') RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.469277;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:297;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9476984;}i:100;a:6:{i:0;s:130:"INSERT INTO "material_production" ("material_id", "quantity", "created_at") VALUES (12, '9', '2025-06-02 15:07:45') RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.470191;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:297;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9479456;}i:102;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.471812;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:314;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9510464;}i:103;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.481861;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:314;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9526416;}i:105;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.4831;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:314;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9521696;}i:106;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.488289;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:314;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9524008;}i:108;a:6:{i:0;s:66:"SELECT * FROM "product" WHERE ("id"=10) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.488879;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:314;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9528152;}i:109;a:6:{i:0;s:66:"SELECT * FROM "product" WHERE ("id"=10) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.491981;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:314;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9531080;}i:111;a:6:{i:0;s:291:"SELECT "pi".*, "m"."name" AS "material_name", "m"."unit_type", "m"."category_id" FROM "product_ingredients" "pi" LEFT JOIN "material" "m" ON m.id = pi.material_id WHERE ("pi"."product_id"=10) AND ("pi"."end_date"='9999-12-31') AND ("pi"."is_alternative"=FALSE) AND ("m"."deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.49482;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:333;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9605648;}i:112;a:6:{i:0;s:291:"SELECT "pi".*, "m"."name" AS "material_name", "m"."unit_type", "m"."category_id" FROM "product_ingredients" "pi" LEFT JOIN "material" "m" ON m.id = pi.material_id WHERE ("pi"."product_id"=10) AND ("pi"."end_date"='9999-12-31') AND ("pi"."is_alternative"=FALSE) AND ("m"."deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.500053;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:333;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9611168;}i:114;a:6:{i:0;s:2825:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_ingredients'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.500631;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:333;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9618440;}i:115;a:6:{i:0;s:2825:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_ingredients'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.508559;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:333;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9631488;}i:117;a:6:{i:0;s:887:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_ingredients'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.509668;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:333;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9628704;}i:118;a:6:{i:0;s:887:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_ingredients'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.515302;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:333;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9631016;}i:120;a:6:{i:0;s:290:"SELECT "pi".*, "m"."name" AS "material_name", "m"."unit_type", "m"."category_id" FROM "product_ingredients" "pi" LEFT JOIN "material" "m" ON m.id = pi.material_id WHERE ("pi"."product_id"=10) AND ("pi"."end_date"='9999-12-31') AND ("pi"."is_alternative"=TRUE) AND ("m"."deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.516365;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:350;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9636648;}i:121;a:6:{i:0;s:290:"SELECT "pi".*, "m"."name" AS "material_name", "m"."unit_type", "m"."category_id" FROM "product_ingredients" "pi" LEFT JOIN "material" "m" ON m.id = pi.material_id WHERE ("pi"."product_id"=10) AND ("pi"."end_date"='9999-12-31') AND ("pi"."is_alternative"=TRUE) AND ("m"."deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.517473;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:350;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9640512;}i:123;a:6:{i:0;s:155:"SELECT * FROM "material_production" WHERE ("material_id" IN (1, 3, 4)) AND ("deleted_at" IS NULL) AND ("quantity" > 0) ORDER BY "material_id", "created_at"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.520506;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:377;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9686288;}i:124;a:6:{i:0;s:155:"SELECT * FROM "material_production" WHERE ("material_id" IN (1, 3, 4)) AND ("deleted_at" IS NULL) AND ("quantity" > 0) ORDER BY "material_id", "created_at"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.521906;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:377;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9690584;}}}";s:2:"db";s:108056:"a:1:{s:8:"messages";a:62:{i:27;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.181765;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8017544;}i:28;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.227739;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8033280;}i:30;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.23057;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8063368;}i:31;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.236356;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8065304;}i:33;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.245182;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8361576;}i:34;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.251036;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8364336;}i:38;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.26072;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8834744;}i:39;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.265153;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8836912;}i:41;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.266322;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8841368;}i:42;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.27066;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8843472;}i:48;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.279718;i:4;a:2:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:35;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9130632;}i:49;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.293109;i:4;a:2:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:35;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9149744;}i:51;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.294322;i:4;a:2:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:35;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9141872;}i:52;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.299861;i:4;a:2:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:35;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9145848;}i:54;a:6:{i:0;s:45:"SELECT * FROM "product_storage" WHERE "id"=76";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.300741;i:4;a:2:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:35;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9149392;}i:55;a:6:{i:0;s:45:"SELECT * FROM "product_storage" WHERE "id"=76";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.305129;i:4;a:2:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:35;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9152400;}i:57;a:6:{i:0;s:180:"SELECT * FROM "product_defect" WHERE (("product_id"=10) AND ("is_repackaging"=TRUE) AND ("deleted_at" IS NULL)) AND ("accepted_at" IS NOT NULL) AND ("accepted_user_id" IS NOT NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.308633;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:153;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:70;s:8:"function";s:22:"getRepackagingProducts";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9211488;}i:58;a:6:{i:0;s:180:"SELECT * FROM "product_defect" WHERE (("product_id"=10) AND ("is_repackaging"=TRUE) AND ("deleted_at" IS NULL)) AND ("accepted_at" IS NOT NULL) AND ("accepted_user_id" IS NOT NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.31914;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:153;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:70;s:8:"function";s:22:"getRepackagingProducts";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9214400;}i:60;a:6:{i:0;s:69:"SELECT * FROM "product_storage_history" WHERE "product_storage_id"=76";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.321214;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:187;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9247840;}i:61;a:6:{i:0;s:69:"SELECT * FROM "product_storage_history" WHERE "product_storage_id"=76";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.326854;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:187;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9251568;}i:63;a:6:{i:0;s:2829:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.327559;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:187;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9260176;}i:64;a:6:{i:0;s:2829:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.339513;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:187;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9278120;}i:66;a:6:{i:0;s:891:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.340602;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:187;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9277720;}i:67;a:6:{i:0;s:891:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.346368;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:187;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9282720;}i:69;a:6:{i:0;s:88:"SELECT * FROM "product_storage_history_materials" WHERE "product_storage_history_id"=334";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.348852;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:200;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9314720;}i:70;a:6:{i:0;s:88:"SELECT * FROM "product_storage_history_materials" WHERE "product_storage_history_id"=334";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.359384;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:200;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9318936;}i:72;a:6:{i:0;s:2839:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_storage_history_materials'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.36003;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:200;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9327560;}i:73;a:6:{i:0;s:2839:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_storage_history_materials'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.372876;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:200;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9342216;}i:75;a:6:{i:0;s:901:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_storage_history_materials'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.374432;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:200;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9338272;}i:76;a:6:{i:0;s:901:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_storage_history_materials'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.379835;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:200;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9342664;}i:78;a:6:{i:0;s:122:"SELECT * FROM "material_production" WHERE ("material_id"=1) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-02')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.381449;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:287;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9372544;}i:79;a:6:{i:0;s:122:"SELECT * FROM "material_production" WHERE ("material_id"=1) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-02')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.385578;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:287;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9374920;}i:81;a:6:{i:0;s:2825:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_production'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.38626;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9380432;}i:82;a:6:{i:0;s:2825:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_production'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.394165;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9393552;}i:84;a:6:{i:0;s:887:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_production'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.395063;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9390800;}i:85;a:6:{i:0;s:887:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_production'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.400007;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:291;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9393112;}i:87;a:6:{i:0;s:129:"INSERT INTO "material_production" ("material_id", "quantity", "created_at") VALUES (1, '9', '2025-06-02 15:07:45') RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.404173;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:297;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9446680;}i:88;a:6:{i:0;s:129:"INSERT INTO "material_production" ("material_id", "quantity", "created_at") VALUES (1, '9', '2025-06-02 15:07:45') RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.431147;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:297;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9449120;}i:90;a:6:{i:0;s:122:"SELECT * FROM "material_production" WHERE ("material_id"=3) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-02')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.432653;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:287;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9450752;}i:91;a:6:{i:0;s:122:"SELECT * FROM "material_production" WHERE ("material_id"=3) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-02')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.433641;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:287;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9453632;}i:93;a:6:{i:0;s:62:"UPDATE "material_production" SET "quantity"='10' WHERE "id"=30";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748858865.434717;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:297;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9462192;}i:94;a:6:{i:0;s:62:"UPDATE "material_production" SET "quantity"='10' WHERE "id"=30";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748858865.465928;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:297;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9464200;}i:96;a:6:{i:0;s:123:"SELECT * FROM "material_production" WHERE ("material_id"=12) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-02')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.466839;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:287;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9465000;}i:97;a:6:{i:0;s:123:"SELECT * FROM "material_production" WHERE ("material_id"=12) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-02')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.468124;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:287;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9467408;}i:99;a:6:{i:0;s:130:"INSERT INTO "material_production" ("material_id", "quantity", "created_at") VALUES (12, '9', '2025-06-02 15:07:45') RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.469277;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:297;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9476984;}i:100;a:6:{i:0;s:130:"INSERT INTO "material_production" ("material_id", "quantity", "created_at") VALUES (12, '9', '2025-06-02 15:07:45') RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.470191;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:297;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:205;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:87;s:8:"function";s:28:"returnMaterialsForOldProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9479456;}i:102;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.471812;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:314;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9510464;}i:103;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.481861;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:314;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9526416;}i:105;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.4831;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:314;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9521696;}i:106;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.488289;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:314;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9524008;}i:108;a:6:{i:0;s:66:"SELECT * FROM "product" WHERE ("id"=10) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.488879;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:314;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9528152;}i:109;a:6:{i:0;s:66:"SELECT * FROM "product" WHERE ("id"=10) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.491981;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:314;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9531080;}i:111;a:6:{i:0;s:291:"SELECT "pi".*, "m"."name" AS "material_name", "m"."unit_type", "m"."category_id" FROM "product_ingredients" "pi" LEFT JOIN "material" "m" ON m.id = pi.material_id WHERE ("pi"."product_id"=10) AND ("pi"."end_date"='9999-12-31') AND ("pi"."is_alternative"=FALSE) AND ("m"."deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.49482;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:333;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9605648;}i:112;a:6:{i:0;s:291:"SELECT "pi".*, "m"."name" AS "material_name", "m"."unit_type", "m"."category_id" FROM "product_ingredients" "pi" LEFT JOIN "material" "m" ON m.id = pi.material_id WHERE ("pi"."product_id"=10) AND ("pi"."end_date"='9999-12-31') AND ("pi"."is_alternative"=FALSE) AND ("m"."deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.500053;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:333;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9611168;}i:114;a:6:{i:0;s:2825:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_ingredients'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.500631;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:333;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9618440;}i:115;a:6:{i:0;s:2825:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product_ingredients'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.508559;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:333;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9631488;}i:117;a:6:{i:0;s:887:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_ingredients'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.509668;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:333;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9628704;}i:118;a:6:{i:0;s:887:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product_ingredients'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.515302;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:333;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9631016;}i:120;a:6:{i:0;s:290:"SELECT "pi".*, "m"."name" AS "material_name", "m"."unit_type", "m"."category_id" FROM "product_ingredients" "pi" LEFT JOIN "material" "m" ON m.id = pi.material_id WHERE ("pi"."product_id"=10) AND ("pi"."end_date"='9999-12-31') AND ("pi"."is_alternative"=TRUE) AND ("m"."deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.516365;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:350;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9636648;}i:121;a:6:{i:0;s:290:"SELECT "pi".*, "m"."name" AS "material_name", "m"."unit_type", "m"."category_id" FROM "product_ingredients" "pi" LEFT JOIN "material" "m" ON m.id = pi.material_id WHERE ("pi"."product_id"=10) AND ("pi"."end_date"='9999-12-31') AND ("pi"."is_alternative"=TRUE) AND ("m"."deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.517473;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:350;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9640512;}i:123;a:6:{i:0;s:155:"SELECT * FROM "material_production" WHERE ("material_id" IN (1, 3, 4)) AND ("deleted_at" IS NULL) AND ("quantity" > 0) ORDER BY "material_id", "created_at"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.520506;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:377;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9686288;}i:124;a:6:{i:0;s:155:"SELECT * FROM "material_production" WHERE ("material_id" IN (1, 3, 4)) AND ("deleted_at" IS NULL) AND ("quantity" > 0) ORDER BY "material_id", "created_at"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748858865.521906;i:4;a:3:{i:0;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:377;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:102:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateReleaseFinishedProductService.php";s:4:"line";i:92;s:8:"function";s:20:"prepareUsedMaterials";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:877;s:8:"function";s:28:"updateReleaseFinishedProduct";s:5:"class";s:71:"app\modules\api\services\manufacter\UpdateReleaseFinishedProductService";s:4:"type";s:2:"->";}}i:5;i:9690584;}}}";s:5:"event";s:15109:"a:81:{i:0;a:5:{s:4:"time";d:1748858865.079086;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:1748858865.090295;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:1748858865.090316;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\api\ApiModule";}i:3;a:5:{s:4:"time";d:1748858865.112257;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:4;a:5:{s:4:"time";d:1748858865.179;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:5;a:5:{s:4:"time";d:1748858865.251507;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\modules\api\models\Users";}i:6;a:5:{s:4:"time";d:1748858865.251596;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\modules\api\models\Users";}i:7;a:5:{s:4:"time";d:1748858865.252022;s:4:"name";s:11:"beforeLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:8;a:5:{s:4:"time";d:1748858865.256599;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:9;a:5:{s:4:"time";d:1748858865.256634;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:10;a:5:{s:4:"time";d:1748858865.256653;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:11;a:5:{s:4:"time";d:1748858865.256667;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:12;a:5:{s:4:"time";d:1748858865.256681;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:13;a:5:{s:4:"time";d:1748858865.256695;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:14;a:5:{s:4:"time";d:1748858865.256709;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:15;a:5:{s:4:"time";d:1748858865.256868;s:4:"name";s:10:"afterLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:16;a:5:{s:4:"time";d:1748858865.256941;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:48:"app\modules\api\controllers\ManufacterController";}i:17;a:5:{s:4:"time";d:1748858865.275404;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:49:"app\modules\api\models\ReleaseFinishedProductForm";}i:18;a:5:{s:4:"time";d:1748858865.275504;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:49:"app\modules\api\models\ReleaseFinishedProductForm";}i:19;a:5:{s:4:"time";d:1748858865.278231;s:4:"name";s:16:"beginTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:20;a:5:{s:4:"time";d:1748858865.279492;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:21;a:5:{s:4:"time";d:1748858865.30567;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\ProductStorage";}i:22;a:5:{s:4:"time";d:1748858865.305751;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\ProductStorage";}i:23;a:5:{s:4:"time";d:1748858865.306562;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:24;a:5:{s:4:"time";d:1748858865.321004;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:25;a:5:{s:4:"time";d:1748858865.327338;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\common\models\ProductStorageHistory";}i:26;a:5:{s:4:"time";d:1748858865.347147;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"app\common\models\ProductStorageHistory";}i:27;a:5:{s:4:"time";d:1748858865.348453;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:28;a:5:{s:4:"time";d:1748858865.359817;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:48:"app\common\models\ProductStorageHistoryMaterials";}i:29;a:5:{s:4:"time";d:1748858865.380446;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:48:"app\common\models\ProductStorageHistoryMaterials";}i:30;a:5:{s:4:"time";d:1748858865.380477;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:48:"app\common\models\ProductStorageHistoryMaterials";}i:31;a:5:{s:4:"time";d:1748858865.380498;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:48:"app\common\models\ProductStorageHistoryMaterials";}i:32;a:5:{s:4:"time";d:1748858865.380504;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:48:"app\common\models\ProductStorageHistoryMaterials";}i:33;a:5:{s:4:"time";d:1748858865.38051;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:48:"app\common\models\ProductStorageHistoryMaterials";}i:34;a:5:{s:4:"time";d:1748858865.381147;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:35;a:5:{s:4:"time";d:1748858865.386031;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:36;a:5:{s:4:"time";d:1748858865.400692;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:37;a:5:{s:4:"time";d:1748858865.403814;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:38;a:5:{s:4:"time";d:1748858865.403844;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:39;a:5:{s:4:"time";d:1748858865.432299;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:40;a:5:{s:4:"time";d:1748858865.432389;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:41;a:5:{s:4:"time";d:1748858865.434135;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:42;a:5:{s:4:"time";d:1748858865.434203;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:43;a:5:{s:4:"time";d:1748858865.434237;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:44;a:5:{s:4:"time";d:1748858865.434476;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:45;a:5:{s:4:"time";d:1748858865.434492;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:46;a:5:{s:4:"time";d:1748858865.466484;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:47;a:5:{s:4:"time";d:1748858865.466558;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:48;a:5:{s:4:"time";d:1748858865.468661;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:49;a:5:{s:4:"time";d:1748858865.468757;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:50;a:5:{s:4:"time";d:1748858865.469032;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:51;a:5:{s:4:"time";d:1748858865.469048;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:52;a:5:{s:4:"time";d:1748858865.470594;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:53;a:5:{s:4:"time";d:1748858865.471554;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:54;a:5:{s:4:"time";d:1748858865.492481;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Product";}i:55;a:5:{s:4:"time";d:1748858865.492559;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Product";}i:56;a:5:{s:4:"time";d:1748858865.493823;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:57;a:5:{s:4:"time";d:1748858865.500456;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\ProductIngredients";}i:58;a:5:{s:4:"time";d:1748858865.515967;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\ProductIngredients";}i:59;a:5:{s:4:"time";d:1748858865.516015;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\ProductIngredients";}i:60;a:5:{s:4:"time";d:1748858865.516069;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\ProductIngredients";}i:61;a:5:{s:4:"time";d:1748858865.516076;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\ProductIngredients";}i:62;a:5:{s:4:"time";d:1748858865.516082;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\ProductIngredients";}i:63;a:5:{s:4:"time";d:1748858865.516127;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:64;a:5:{s:4:"time";d:1748858865.517938;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\ProductIngredients";}i:65;a:5:{s:4:"time";d:1748858865.518036;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\ProductIngredients";}i:66;a:5:{s:4:"time";d:1748858865.518899;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:67;a:5:{s:4:"time";d:1748858865.522287;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:68;a:5:{s:4:"time";d:1748858865.522336;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:69;a:5:{s:4:"time";d:1748858865.522355;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:70;a:5:{s:4:"time";d:1748858865.522371;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:71;a:5:{s:4:"time";d:1748858865.522377;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:72;a:5:{s:4:"time";d:1748858865.522382;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:73;a:5:{s:4:"time";d:1748858865.522885;s:4:"name";s:19:"rollbackTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:74;a:5:{s:4:"time";d:1748858865.523514;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:48:"app\modules\api\controllers\ManufacterController";}i:75;a:5:{s:4:"time";d:1748858865.524416;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\api\ApiModule";}i:76;a:5:{s:4:"time";d:1748858865.524431;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:77;a:5:{s:4:"time";d:1748858865.524447;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:78;a:5:{s:4:"time";d:1748858865.524458;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:79;a:5:{s:4:"time";d:1748858865.526352;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:80;a:5:{s:4:"time";d:1748858865.526457;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:**********.974328;s:3:"end";d:1748858865.531468;s:6:"memory";i:9975408;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:2262:"a:3:{s:8:"messages";a:11:{i:9;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748858865.079863;i:4;a:0:{}i:5;i:5773264;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748858865.079887;i:4;a:0:{}i:5;i:5774016;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748858865.079899;i:4;a:0:{}i:5;i:5774768;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748858865.079906;i:4;a:0:{}i:5;i:5775520;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748858865.079918;i:4;a:0:{}i:5;i:5776272;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748858865.079927;i:4;a:0:{}i:5;i:5777024;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:5:"login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748858865.079932;i:4;a:0:{}i:5;i:5777776;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:6:"logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748858865.079997;i:4;a:0:{}i:5;i:5778528;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:13:"site/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748858865.080004;i:4;a:0:{}i:5;i:5779920;}i:18;a:6:{i:0;s:69:"Request parsed with URL rule: api/<controller:[\w-]+>/<action:[\w-]+>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:1748858865.080029;i:4;a:0:{}i:5;i:5782040;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:39:"api/<controller:[\w-]+>/<action:[\w-]+>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748858865.080036;i:4;a:0:{}i:5;i:5781832;}}s:5:"route";s:46:"api/manufacter/update-release-finished-product";s:6:"action";s:86:"app\modules\api\controllers\ManufacterController::actionUpdateReleaseFinishedProduct()";}";s:7:"request";s:4645:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:11:{s:13:"authorization";s:48:"Bearer  681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy";s:12:"content-type";s:16:"application/json";s:10:"user-agent";s:21:"PostmanRuntime/7.44.0";s:6:"accept";s:3:"*/*";s:13:"cache-control";s:8:"no-cache";s:13:"postman-token";s:36:"1e25c93c-7c54-4ebe-94db-8e68358c2443";s:4:"host";s:6:"silver";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:3:"186";s:6:"cookie";s:216:"PHPSESSID=5kmna394fkk624c3mdo5a4tcspgksban; _csrf=38e423d673615b11703d8407f7264580fe19cead6b1dce90f78006a78a8e1a72a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22hGZNaGfhjH8CR8k50afGh1HKEsx8t4oR%22%3B%7D";}s:15:"responseHeaders";a:9:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:4:"Vary";s:6:"Accept";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"683d77f110218";s:16:"X-Debug-Duration";s:3:"553";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=683d77f110218";s:10:"Set-Cookie";s:204:"_csrf=f783708165795399b53520eb6ff38469e729ee4c74fd5517737051ad52701622a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22G6hDdT4U9akOzndNW99TA9k0M80aXsdb%22%3B%7D; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:46:"api/manufacter/update-release-finished-product";s:6:"action";s:86:"app\modules\api\controllers\ManufacterController::actionUpdateReleaseFinishedProduct()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:16:"application/json";s:3:"Raw";s:186:"    {
        "product_storage_id": 76,
        "products":
        [
            {
                "product_id": 10,
                "quantity": 5
            }
        ]
    }";s:7:"Decoded";a:2:{s:18:"product_storage_id";i:76;s:8:"products";a:1:{i:0;a:2:{s:10:"product_id";i:10;s:8:"quantity";i:5;}}}}s:6:"SERVER";a:39:{s:15:"REDIRECT_STATUS";s:3:"200";s:12:"CONTENT_TYPE";s:16:"application/json";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.0";s:11:"HTTP_ACCEPT";s:3:"*/*";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:18:"HTTP_POSTMAN_TOKEN";s:36:"1e25c93c-7c54-4ebe-94db-8e68358c2443";s:9:"HTTP_HOST";s:6:"silver";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:3:"186";s:11:"HTTP_COOKIE";s:216:"PHPSESSID=5kmna394fkk624c3mdo5a4tcspgksban; _csrf=38e423d673615b11703d8407f7264580fe19cead6b1dce90f78006a78a8e1a72a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22hGZNaGfhjH8CR8k50afGh1HKEsx8t4oR%22%3B%7D";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:6:"silver";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:44:"D:/OSPanel/domains/silverzavod/web/index.php";s:11:"REMOTE_PORT";s:5:"54196";s:12:"REDIRECT_URL";s:47:"/api/manufacter/update-release-finished-product";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:47:"/api/manufacter/update-release-finished-product";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.93795;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:2:{s:9:"PHPSESSID";s:32:"5kmna394fkk624c3mdo5a4tcspgksban";s:5:"_csrf";s:130:"38e423d673615b11703d8407f7264580fe19cead6b1dce90f78006a78a8e1a72a:2:{i:0;s:5:"_csrf";i:1;s:32:"hGZNaGfhjH8CR8k50afGh1HKEsx8t4oR";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:1:{s:7:"__flash";a:0:{}}}";s:4:"user";s:2147:"a:5:{s:2:"id";i:7;s:8:"identity";a:8:{s:2:"id";s:1:"7";s:8:"username";s:14:"'manufacturer'";s:9:"full_name";s:20:"'Ishlab chiqaruvchi'";s:4:"role";s:1:"0";s:12:"access_token";s:42:"'681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";s:8:"password";s:62:"'$2y$13$8ymQqJl5qbjAvbz9d5fbFuOFZGcDCeN6PAzjPCDDcPlYo7inyMDbG'";s:10:"created_at";s:21:"'2025-03-17 10:58:05'";s:10:"deleted_at";s:4:"null";}s:10:"attributes";a:8:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:10:"Логин";}i:2;a:2:{s:9:"attribute";s:9:"full_name";s:5:"label";s:9:"Full Name";}i:3;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:4;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}i:5;a:2:{s:9:"attribute";s:8:"password";s:5:"label";s:10:"Парол";}i:6;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:7;a:2:{s:9:"attribute";s:10:"deleted_at";s:5:"label";s:10:"Deleted At";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";N;s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:1:{s:12:"manufacturer";a:7:{s:4:"type";i:1;s:4:"name";s:12:"manufacturer";s:11:"description";s:12:"Manufacturer";s:8:"ruleName";N;s:4:"data";s:4:"null";s:9:"createdAt";i:**********;s:9:"updatedAt";i:**********;}}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"683d77f110218";s:3:"url";s:60:"http://silver/api/manufacter/update-release-finished-product";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:**********.93795;s:10:"statusCode";i:200;s:8:"sqlCount";i:31;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9882096;s:14:"processingTime";d:0.****************;}s:10:"exceptions";a:0:{}}