<?php

namespace app\modules\api\controllers;

use app\common\models\Material;
use app\common\models\MaterialStorage;
use app\common\models\Tracking;
use app\modules\api\models\AcceptIncomeMaterialForm;
use Yii;
use yii\web\ForbiddenHttpException;
use app\common\models\ApiResponse;
use app\modules\api\controllers\BaseController;
use app\common\models\ActionLogger;
use app\common\models\MaterialDefect;
use app\common\models\MaterialStorageHistory;
use app\common\models\MaterialStatus;
use app\common\models\Product;
use app\common\models\ProductStorage;
use app\modules\api\models\SendToMaterialDefectForm;
use app\modules\api\models\ReleaseFinishedProductForm;
use app\common\models\MaterialProduction;
use app\common\models\ProductIngredients;
use app\common\models\ProductStorageHistory;
use app\common\models\MaterialStatusGroup;
use app\common\models\ProductDefect;
use app\modules\api\services\manufacter\ReleaseFinishedProductService;
use app\modules\api\services\manufacter\UpdateReleaseFinishedProductService;
use app\modules\api\services\manufacter\DeleteReleaseFinishedProductService;
use app\modules\api\services\manufacter\SendToMaterialDefectService;
use app\modules\api\services\manufacter\UpdateSendToMaterialDefectService;
use app\modules\api\services\manufacter\DeleteSendToMaterialDefectService;
use PDO;

class ManufacterController extends BaseController
{
    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        try {
            if (!Yii::$app->user->can('manufacturer')) {
                throw new ForbiddenHttpException(Yii::t('app', "You don\'t have permission to access this resource"));
            }
        } catch (ForbiddenHttpException $e) {
            Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
            Yii::$app->response->data = ApiResponse::response(
                $e->getMessage(),
                null,
                403
            );
            Yii::$app->response->statusCode = 403;
            return false;
        }

        return true;
    }

    public function actionIndex()
    {
        $today = date('Y-m-d');

        $receivedMaterialsSql = "
           SELECT
                m.name as material_name,
                to_char(SUM(mp.quantity), 'FM999999999.########') as quantity,
                m.unit_type,
                CASE
                    WHEN m.unit_type = 1 THEN 'Штука'
                    WHEN m.unit_type = 2 THEN 'Килограмм'
                    WHEN m.unit_type = 3 THEN 'Литр'
                    ELSE 'Неизвестно'
                END as unit_type_name
            FROM material_production mp
            JOIN material m ON m.id = mp.material_id
            WHERE mp.deleted_at IS NULL
            GROUP BY m.id, m.name, m.unit_type
            ORDER BY m.name

        ";

        $producedProductsSql = "
            WITH ProductQuantities AS (
                SELECT
                    p.id AS product_id,
                    p.name AS product_name,
                    COALESCE(SUM(ps.quantity), 0) AS product_quantity
                FROM product_storage ps
                JOIN product p ON p.id = ps.product_id
                WHERE DATE(ps.enter_date) = :today
                GROUP BY p.id, p.name
            ),
            MaterialUsage AS (
                SELECT
                    pq.product_id,
                    pq.product_name,
                    pq.product_quantity,
                    m.name AS material_name,
                    pq.product_quantity AS material_quantity -- Виртуальное значение 1
                FROM ProductQuantities pq
                JOIN product_ingredients pi ON pi.product_id = pq.product_id
                JOIN material m ON m.id = pi.material_id
                WHERE (pi.end_date IS NULL OR pi.end_date > NOW())
            )
            SELECT
                mu.product_name,
                mu.product_quantity AS quantity,
                json_agg(json_build_object('material_name', mu.material_name, 'material_quantity', mu.material_quantity)) AS used_materials
            FROM MaterialUsage mu
            GROUP BY mu.product_id, mu.product_name, mu.product_quantity
            ORDER BY mu.product_name;
        ";        $defectMaterialsSql = "
            SELECT
                m.name as material_name,
                to_char(SUM(md.quantity), 'FM999999999.########') as quantity,
                m.unit_type,
                CASE
                    WHEN m.unit_type = 1 THEN 'Штука'
                    WHEN m.unit_type = 2 THEN 'Килограмм'
                    WHEN m.unit_type = 3 THEN 'Литр'
                    ELSE 'Неизвестно'
                END as unit_type_name
            FROM material_defect md
            JOIN material m ON m.id = md.material_id
            WHERE DATE(md.created_at) = :today
            AND md.deleted_at IS NULL
            GROUP BY m.id, m.name, m.unit_type
            ORDER BY m.name
        ";

        $returnedMaterialsSql = "
            SELECT
                m.name as material_name,
                to_char(SUM(ms.quantity), 'FM999999999.########') as quantity,
                m.unit_type,
                CASE
                    WHEN m.unit_type = 1 THEN 'Штука'
                    WHEN m.unit_type = 2 THEN 'Килограмм'
                    WHEN m.unit_type = 3 THEN 'Литр'
                    ELSE 'Неизвестно'
                END as unit_type_name
            FROM material_status_group msg
            JOIN material_status ms ON ms.status_group_id = msg.id
            JOIN material m ON m.id = ms.material_id
            WHERE DATE(msg.created_at) = :today
            AND msg.deleted_at IS NULL
            AND ms.deleted_at IS NULL
            AND msg.status = :return_status
            GROUP BY m.id, m.name, m.unit_type
            ORDER BY m.name
        ";


        $receivedMaterials = Yii::$app->db->createCommand($receivedMaterialsSql)
            ->queryAll();

        $producedProducts = Yii::$app->db->createCommand($producedProductsSql)
            ->bindValue(':today', $today)
            ->queryAll();        // Преобразуем `used_materials` из строки в массив



        foreach ($producedProducts as &$product) {
            if (isset($product['used_materials'])) {
                $product['used_materials'] = json_decode($product['used_materials'], true);
            }
        }

        $defectMaterials = Yii::$app->db->createCommand($defectMaterialsSql)
            ->bindValue(':today', $today)
            ->queryAll();

        $returnedMaterials = Yii::$app->db->createCommand($returnedMaterialsSql)
            ->bindValue(':today', $today)
            ->bindValue(':return_status', MaterialStatusGroup::STATUS_RETURNED_TO_SUPPLIER)
            ->queryAll();

        return ApiResponse::response('Производственный отчет за сегодня', [
            'received_materials' => $receivedMaterials,
            'produced_products' => $producedProducts,
            'defect_materials' => $defectMaterials,
            'returned_materials' => $returnedMaterials
        ]);
    }


    public function actionAcceptIncomeMaterial()
    {
        if (Yii::$app->request->isGet) {
            $date = Yii::$app->request->get('date') ?: date('Y-m-d');

            $sql = "
            WITH GroupedMaterials AS (
                SELECT
                    msg.id as group_id,
                    msg.created_at,
                    msg.status,
                    msg.accepted_at,
                    msg.accepted_user_id,
                    u.username as user_name,
                    json_agg(
                        json_build_object(
                            'material_status_id', ms.id,
                            'material_id', m.id,
                            'material_name', m.name,
                            'quantity', ms.quantity,
                            'unit_type', m.unit_type,
                            'unit_type_name', CASE
                                WHEN m.unit_type = 1 THEN 'Штука'
                                WHEN m.unit_type = 2 THEN 'Килограмм'
                                WHEN m.unit_type = 3 THEN 'Литр'
                                ELSE 'Неизвестно'
                            END
                        )
                    ) as materials
                FROM material_status_group msg
                JOIN material_status ms ON ms.status_group_id = msg.id
                JOIN material m ON m.id = ms.material_id
                LEFT JOIN users u ON u.id = msg.add_user_id
                WHERE msg.deleted_at IS NULL
                AND ms.deleted_at IS NULL
                AND msg.status = :status
                AND DATE(msg.created_at) = :date
                GROUP BY msg.id, msg.created_at, msg.status, msg.accepted_at, msg.accepted_user_id, u.username
            )
            SELECT
                group_id,
                created_at,
                accepted_at,
                user_name,
                CASE
                    WHEN accepted_at IS NOT NULL AND accepted_user_id IS NOT NULL THEN true
                    ELSE false
                END as is_accepted,
                materials
            FROM GroupedMaterials
            ORDER BY created_at DESC
            ";

            $command = Yii::$app->db->createCommand($sql);
            $command->bindValue(':date', $date);
            $command->bindValue(':status', MaterialStatusGroup::STATUS_IN_PRODUCTION);
            $pendingMaterials = $command->queryAll();

            foreach ($pendingMaterials as &$group) {
                $group['materials'] = json_decode($group['materials'], true);
            }

            return ApiResponse::response('Материалы для принятия в производство', [
                'pending_materials' => $pendingMaterials
            ]);
        }

        if (Yii::$app->request->isPost) {
            $group_id = Yii::$app->request->post('group_id');

            if (!$group_id) {
                return ApiResponse::response(
                    'ID группы обязателен',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }


            $transaction = Yii::$app->db->beginTransaction();
            try {
                $group = MaterialStatusGroup::find()
                    ->where([
                        'id' => $group_id,
                        'status' => MaterialStatusGroup::STATUS_IN_PRODUCTION,
                        'deleted_at' => null
                    ])
                    ->andWhere(['IS', 'accepted_at', null])
                    ->andWhere(['IS', 'accepted_user_id', null])
                    ->one();

                if (!$group) {
                    throw new \Exception('Группа не найдена или уже принята');
                }

                $materialStatuses = MaterialStatus::find()
                    ->where([
                        'status_group_id' => $group->id,
                        'deleted_at' => null
                    ])
                    ->all();

                foreach ($materialStatuses as $materialStatus) {
                    $materialStorages = MaterialStorage::find()
                        ->where(['material_id' => $materialStatus->material_id])
                        ->andWhere(['IS', 'deleted_at', null])
                        ->andWhere(['>=', 'quantity', 0])
                        ->orderBy(['created_at' => SORT_ASC])
                        ->all();

                    if (!$materialStorages) {
                        throw new \Exception("Материал #{$materialStatus->material_id} не найден в складе");
                    }

                    $remainingQuantity = $materialStatus->quantity;
                    foreach ($materialStorages as $storage) {
                        if ($remainingQuantity <= 0) break;

                        $quantityToDeduct = min($storage->quantity, $remainingQuantity);
                        $storage->quantity -= $quantityToDeduct;
                        $remainingQuantity -= $quantityToDeduct;

                        if (!$storage->save()) {
                            throw new \Exception(json_encode($storage->getErrors()));
                        }
                    }

                    $storageHistory = new MaterialStorageHistory();
                    $storageHistory->material_storage_id = $materialStorages[0]->id;
                    $storageHistory->material_id = $materialStatus->material_id;
                    $storageHistory->quantity = $materialStatus->quantity;
                    $storageHistory->created_at = date('Y-m-d H:i:s');
                    $storageHistory->add_user_id = Yii::$app->user->id;
                    $storageHistory->type = MaterialStorageHistory::TYPE_INCOME_TO_PRDUCTION;

                    if (!$storageHistory->save()) {
                        throw new \Exception(json_encode($storageHistory->getErrors()));
                    }

                    $today = date('Y-m-d');
                    $materialProduction = MaterialProduction::find()
                        ->where([
                            'material_id' => $materialStatus->material_id,
                            'DATE(created_at)' => $today
                        ])
                        ->one();

                    if ($materialProduction) {
                        $materialProduction->quantity += $materialStatus->quantity;
                        if (!$materialProduction->save()) {
                            throw new \Exception(json_encode($materialProduction->getErrors()));
                        }
                    } else {
                        $materialProduction = new MaterialProduction();
                        $materialProduction->material_id = $materialStatus->material_id;
                        $materialProduction->quantity = $materialStatus->quantity;
                        $materialProduction->created_at = date('Y-m-d H:i:s');

                        if (!$materialProduction->save()) {
                            throw new \Exception(json_encode($materialProduction->getErrors()));
                        }
                    }
                }

                $tracking = new Tracking();
                $tracking->progress_type = Tracking::TYPE_ACCEPT_PRODUCTION_MATERIAL;
                $tracking->process_id = $group->id;
                $tracking->created_at = date('Y-m-d H:i:s');
                $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
                $tracking->accepted_at = null;

                if (!$tracking->save()) {
                    throw new \Exception(json_encode($tracking->getErrors()));
                }

                $group->accepted_at = date('Y-m-d H:i:s');
                $group->accepted_user_id = Yii::$app->user->id;

                if (!$group->save()) {
                    throw new \Exception(json_encode($group->getErrors()));
                }

                ActionLogger::actionLog(
                    'accept_production_material',
                    'material_status_group',
                    $group->id,
                    [
                        'materials' => array_map(function($status) {
                            return [
                                'material_id' => $status->material_id,
                                'quantity' => $status->quantity
                            ];
                        }, $materialStatuses),
                        'tracking_id' => $tracking->id
                    ]
                );

                $transaction->commit();
                return ApiResponse::response('Материалы успешно приняты в производство');

            } catch (\Exception $e) {
                $transaction->rollBack();
                return ApiResponse::response(
                    'Ошибка при принятии материалов',
                    ['error' => $e->getMessage()],
                    ApiResponse::HTTP_INTERNAL_SERVER_ERROR
                );
            }
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }

    public function actionSendToMaterialDefect()
    {
        if (Yii::$app->request->isGet) {
            $date = Yii::$app->request->get('date');

            // vaqtincha material_storage dan olinadi keyinchalik material_production dan olinadi
            $sql = "
            SELECT
                m.id as material_id,
                m.name as material_name,
                to_char(SUM(mp.quantity), 'FM999999999.########') as quantity,
                m.unit_type,
                CASE
                    WHEN m.unit_type = 1 THEN 'Штука'
                    WHEN m.unit_type = 2 THEN 'Килограмм'
                    WHEN m.unit_type = 3 THEN 'Литр'
                    ELSE 'Неизвестно'
                END as unit_type_name
            FROM material_storage mp
            JOIN material m ON m.id = mp.material_id
            WHERE mp.deleted_at IS NULL
            AND mp.quantity > 0
            " . (!empty($date) ? "AND mp.created_at >= :date_start AND mp.created_at < :date_end" : "") . "
            GROUP BY m.id, m.name, m.unit_type
            ORDER BY m.name
        ";

        $command = Yii::$app->db->createCommand($sql);

        if (!empty($date)) {
            $dateStart = $date . ' 00:00:00';
            $dateEnd = $date . ' 23:59:59';
            $command->bindValue(':date_start', $dateStart, PDO::PARAM_STR);
            $command->bindValue(':date_end', $dateEnd, PDO::PARAM_STR);
        }

        $availableMaterials = $command->queryAll();



          $defectDate = !empty($date) ? $date : date('Y-m-d');

                $defectSql = "
                    SELECT
                        md.id as defect_id,
                        m.name as material_name,
                        to_char(md.quantity, 'FM999999999.########') as quantity,
                        md.quantity,
                        md.description,
                        md.created_at,
                        m.unit_type,
                        CASE
                            WHEN m.unit_type = 1 THEN 'Штука'
                            WHEN m.unit_type = 2 THEN 'Килограмм'
                            WHEN m.unit_type = 3 THEN 'Литр'
                            ELSE 'Неизвестно'
                        END as unit_type_name,
                        CASE
                            WHEN md.accepted_user_id IS NOT NULL THEN true
                            ELSE false
                        END as is_accepted,
                        md.is_processed,
                        u.full_name as added_by,
                        au.full_name as accepted_by
                    FROM material_defect md
                    JOIN material m ON m.id = md.material_id
                    LEFT JOIN users u ON u.id = md.add_user_id
                    LEFT JOIN users au ON au.id = md.accepted_user_id
                    WHERE md.deleted_at IS NULL AND md.source = :source
                    AND md.add_user_id = :user_id
                    " . (!empty($date) ? "AND md.created_at >= :date_start AND md.created_at < :date_end" : "AND DATE(md.created_at) = :defect_date") . "
                    ORDER BY md.created_at DESC
                ";

                $defectCommand = Yii::$app->db->createCommand($defectSql);

                $defectCommand->bindValue(':user_id', Yii::$app->user->id);
                $defectCommand->bindValue(':source', MaterialDefect::SOURCE_MANUFACTURER);

                if (!empty($date)) {
                    $dateStart = $date . ' 00:00:00';
                    $dateEnd = $date . ' 23:59:59';
                    $defectCommand->bindValue(':date_start', $dateStart, PDO::PARAM_STR);
                    $defectCommand->bindValue(':date_end', $dateEnd, PDO::PARAM_STR);
                } else {
                    $defectCommand->bindValue(':defect_date', $defectDate, PDO::PARAM_STR);
                }

                $defectMaterials = $defectCommand->queryAll();

                // Получаем группы возврата материалов
                $returnSql = "
                    SELECT
                        msg.id as group_id,
                        msg.created_at,
                        msg.accepted_at,
                        CASE
                            WHEN msg.accepted_user_id IS NOT NULL THEN true
                            ELSE false
                        END as is_accepted,
                        u.full_name as added_by,
                        au.full_name as accepted_by
                    FROM material_status_group msg
                    LEFT JOIN users u ON u.id = msg.add_user_id
                    LEFT JOIN users au ON au.id = msg.accepted_user_id
                    WHERE msg.deleted_at IS NULL
                    AND msg.status = 5
                    AND msg.add_user_id = :user_id
                    " . (!empty($date) ? "AND msg.created_at >= :date_start AND msg.created_at < :date_end" : "AND DATE(msg.created_at) = :defect_date") . "
                    ORDER BY msg.created_at DESC
                ";

                $returnCommand = Yii::$app->db->createCommand($returnSql);
                $returnCommand->bindValue(':user_id', Yii::$app->user->id);

                if (!empty($date)) {
                    $returnCommand->bindValue(':date_start', $dateStart, PDO::PARAM_STR);
                    $returnCommand->bindValue(':date_end', $dateEnd, PDO::PARAM_STR);
                } else {
                    $returnCommand->bindValue(':defect_date', $defectDate, PDO::PARAM_STR);
                }

                $returnGroups = $returnCommand->queryAll();

                // Получаем материалы для каждой группы возврата
                $returnMaterials = [];
                foreach ($returnGroups as $group) {
                    $materialsSql = "
                        SELECT
                            ms.material_id,
                            m.name as material_name,
                            to_char(ms.quantity, 'FM999999999.########') as quantity,
                            m.unit_type,
                            CASE
                                WHEN m.unit_type = 1 THEN 'Штука'
                                WHEN m.unit_type = 2 THEN 'Килограмм'
                                WHEN m.unit_type = 3 THEN 'Литр'
                                ELSE 'Неизвестно'
                            END as unit_type_name
                        FROM material_status ms
                        JOIN material m ON m.id = ms.material_id
                        WHERE ms.status_group_id = :group_id
                        AND ms.deleted_at IS NULL
                        ORDER BY m.name
                    ";

                    $materialsCommand = Yii::$app->db->createCommand($materialsSql);
                    $materialsCommand->bindValue(':group_id', $group['group_id']);
                    $groupMaterials = $materialsCommand->queryAll();

                    // Добавляем материалы к группе
                    $group['materials'] = $groupMaterials;
                    $returnMaterials[] = $group;
                }

                return ApiResponse::response('Материалы для брака', [
                    'available_materials' => $availableMaterials,
                    'defect_materials' => $defectMaterials,
                    'return_materials' => $returnMaterials
                ]);
        }

        if (Yii::$app->request->isPost) {
            $service = new SendToMaterialDefectService();
            $result = $service->sendToMaterialDefect(Yii::$app->request->post());

            if ($result['success']) {
                return ApiResponse::response($result['message']);
            } else {
                if (isset($result['errors'])) {
                    return ApiResponse::response(
                        $result['message'],
                        $result['errors'],
                        ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                    );
                } else {
                    return ApiResponse::response(
                        $result['message'],
                        ['error' => $result['message']],
                        ApiResponse::HTTP_INTERNAL_SERVER_ERROR
                    );
                }
            }
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }

    public function actionUpdateSendToMaterialDefect()
    {
        if (Yii::$app->request->isGet) {
            $material_defect_id = Yii::$app->request->get('material_defect_id');
            $group_id = Yii::$app->request->get('group_id');
            $type = Yii::$app->request->get('type', 'defect'); // По умолчанию 'defect'

            // Определяем ID и тип операции
            $id = $type === 'return' ? $group_id : $material_defect_id;

            if (!$id) {
                return ApiResponse::response(
                    'ID записи обязателен',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            // Используем сервис для получения информации о записи брака материала или возврата
            $service = new UpdateSendToMaterialDefectService();
            $result = $service->getMaterialDefect($id, $type);

            if ($result['success']) {
                return ApiResponse::response('', [
                    'material_defect' => $result['material_defect']
                ]);
            } else {
                return ApiResponse::response(
                    $result['message'],
                    null,
                    ApiResponse::HTTP_NOT_FOUND
                );
            }
        }

        if (Yii::$app->request->isPost) {
            $service = new UpdateSendToMaterialDefectService();
            $result = $service->updateMaterialDefect(Yii::$app->request->post());

            if ($result['success']) {
                return ApiResponse::response($result['message']);
            } else {
                if (isset($result['errors'])) {
                    return ApiResponse::response(
                        $result['message'],
                        $result['errors'],
                        ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                    );
                } else {
                    return ApiResponse::response(
                        $result['message'],
                        ['error' => $result['message']],
                        ApiResponse::HTTP_INTERNAL_SERVER_ERROR
                    );
                }
            }
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }



    public function actionDeleteSendToMaterialDefect()
    {
        if (Yii::$app->request->isPost) {
            $material_defect_id = Yii::$app->request->post('material_defect_id');
            $group_id = Yii::$app->request->post('group_id');
            $type = Yii::$app->request->post('type', 'defect'); // По умолчанию 'defect'

            // Определяем ID и тип операции
            $id = $type === 'return' ? $group_id : $material_defect_id;

            if (!$id) {
                return ApiResponse::response(
                    'ID записи обязателен',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            // Используем сервис для удаления записи о браке материала или возврате
            $service = new DeleteSendToMaterialDefectService();
            $result = $service->deleteMaterialDefect($id, $type);

            if ($result['success']) {
                return ApiResponse::response($result['message']);
            } else {
                return ApiResponse::response(
                    $result['message'],
                    ['error' => $result['message']],
                    ApiResponse::HTTP_INTERNAL_SERVER_ERROR
                );
            }
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }




    public function actionReleaseFinishedProduct()
    {
        if (Yii::$app->request->isGet) {
            $date = Yii::$app->request->get('date') ?: date('Y-m-d');

            $products = Product::find()
            ->select([
                'product.id as product_id',
                'product.name as product_name',
                'product.size',
                'storage_quantity' => 'COALESCE(SUM(ps.quantity), 0)'
            ])
            ->from('product')
            ->leftJoin('product_storage ps', 'ps.product_id = product.id AND ps.deleted_at IS NULL')
            ->where(['product.deleted_at' => null])
            ->groupBy(['product.id', 'product.name', 'product.size'])
            ->orderBy(['product.name' => SORT_ASC])
            ->asArray()
            ->all();

            $releasedSql = "
                SELECT
                    ps.id as product_storage_id,
                    p.name as product_name,
                    ps.quantity,
                    ps.enter_date,
                    u.full_name as accepted_by,
                    ps.accepted_at,
                    CASE
                        WHEN ps.accepted_at IS NOT NULL AND ps.accepted_user_id IS NOT NULL THEN true
                        ELSE false
                    END as is_accepted,
                    u1.full_name as out_person
                FROM product_storage ps
                JOIN product p ON p.id = ps.product_id
                LEFT JOIN users u ON u.id = ps.accepted_user_id
                left join users u1 on u1.id = ps.add_user_id
                WHERE ps.deleted_at IS NULL AND DATE(ps.enter_date) = :date
                ORDER BY ps.enter_date DESC
            ";

            $releasedProducts = Yii::$app->db->createCommand($releasedSql)
                ->bindValue(':date', $date)
                ->queryAll();

            return ApiResponse::response('Список продуктов', [
                'products' => $products,
                'released_products' => $releasedProducts
            ]);
        }

        if (Yii::$app->request->isPost) {
            $model = new ReleaseFinishedProductForm();
            $model->load(Yii::$app->request->post(), '');

            if (!$model->validate()) {
                return ApiResponse::response(
                    'Ошибка валидации',
                    $model->getErrors(),
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            $productId = $model['products'][0]['product_id'];
            $quantity = $model['products'][0]['quantity'];

            // Используем сервис для выпуска продукции
            $service = new ReleaseFinishedProductService();
            $result = $service->releaseFinishedProduct($productId, $quantity);

            if ($result['success']) {
                return ApiResponse::response($result['message'], null, ApiResponse::HTTP_OK);
            } else {
                return ApiResponse::response(
                    $result['message'],
                    ['error' => $result['error']],
                    ApiResponse::HTTP_NOT_FOUND
                );
            }
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }

    public function actionUpdateReleaseFinishedProduct()
    {
        if (Yii::$app->request->isGet) {
            $product_storage_id = Yii::$app->request->get('product_storage_id');

            if (!$product_storage_id) {
                return ApiResponse::response(
                    'ID записи обязателен',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            $sql = "
                SELECT
                    ps.id as product_storage_id,
                    p.id as product_id,
                    p.name as product_name,
                    ps.quantity,
                    ps.enter_date,
                    u1.full_name as added_by,
                    u.full_name as accepted_by,
                    ps.accepted_at,
                    t.status,
                    CASE
                        WHEN ps.accepted_at IS NOT NULL AND ps.accepted_user_id IS NOT NULL THEN true
                        ELSE false
                    END as is_accepted
                FROM product_storage ps
                JOIN product p ON p.id = ps.product_id
                LEFT JOIN users u ON u.id = ps.accepted_user_id
                LEFT JOIN users u1 ON u1.id = ps.add_user_id
                LEFT JOIN tracking t ON t.process_id = ps.id AND t.progress_type = :progress_type AND t.deleted_at IS NULL
                WHERE ps.id = :product_storage_id
                AND ps.deleted_at IS NULL
            ";

            $productStorage = Yii::$app->db->createCommand($sql)
                ->bindValue(':product_storage_id', $product_storage_id)
                ->bindValue(':progress_type', Tracking::TYPE_PRODUCT_RELEASE)
                ->queryOne();

            if (!$productStorage) {
                return ApiResponse::response(
                    'Запись не найдена или уже удалена',
                    null,
                    ApiResponse::HTTP_NOT_FOUND
                );
            }

            return ApiResponse::response('', [
                'product_storage' => $productStorage
            ]);
        }

        if (Yii::$app->request->isPost) {
            $product_storage_id = Yii::$app->request->post('product_storage_id');

            if (!$product_storage_id) {
                return ApiResponse::response(
                    'ID записи обязателен',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            $model = new ReleaseFinishedProductForm();
            $model->load(Yii::$app->request->post(), '');

            $product_id = $model['products'][0]['product_id'];
            $quantity = $model['products'][0]['quantity'];

            if (!$model->validate()) {
                return ApiResponse::response(
                    'Ошибка валидации',
                    $model->getErrors(),
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            // Используем сервис для обновления выпущенной продукции
            $service = new UpdateReleaseFinishedProductService();
            $result = $service->updateReleaseFinishedProduct($product_storage_id, $product_id, $quantity);

            if ($result['success']) {
                return ApiResponse::response($result['message'], null, ApiResponse::HTTP_OK);
            } else {
                return ApiResponse::response(
                    $result['message'],
                    ['error' => $result['error']],
                    ApiResponse::HTTP_OK
                );
            }
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }

    public function actionDeleteReleaseFinishedProduct()
    {
        if (Yii::$app->request->isPost) {
            $product_storage_id = Yii::$app->request->post('product_storage_id');

            if (!$product_storage_id) {
                return ApiResponse::response(
                    'ID записи обязателен',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            // Используем сервис для удаления выпущенной продукции
            $service = new DeleteReleaseFinishedProductService();
            $result = $service->deleteReleaseFinishedProduct($product_storage_id);

            if ($result['success']) {
                return ApiResponse::response($result['message']);
            } else {
                return ApiResponse::response(
                    $result['message'],
                    ['error' => $result['error']],
                    ApiResponse::HTTP_INTERNAL_SERVER_ERROR
                );
            }
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }



    public function actionGetRepackagingProducts()
    {
        if (Yii::$app->request->isGet) {
            $date = Yii::$app->request->get('date', date('Y-m-d'));

            $query = ProductDefect::find()
                ->select([
                    'product_defect.id',
                    'product_defect.created_at',
                    'u.username as user_name',
                    'p.name as product_name',
                    'product_defect.quantity',
                    'product_defect.repackaging_reason',
                    'product_defect.description',
                    'au.username as accepted_by',
                    new \yii\db\Expression('CASE
                        WHEN product_defect.accepted_user_id IS NOT NULL
                        THEN true
                        ELSE false
                    END as is_accepted'),
                    new \yii\db\Expression('CASE
                        WHEN t.accepted_at IS NOT NULL
                        AND t.status = ' . Tracking::STATUS_ACCEPTED . '
                        AND t.deleted_at IS NULL
                        THEN true
                        ELSE false
                    END as status')
                ])
                ->leftJoin('product p', 'p.id = product_defect.product_id')
                ->leftJoin('users u', 'u.id = product_defect.add_user_id')
                ->leftJoin('users au', 'au.id = product_defect.accepted_user_id')
                ->leftJoin('tracking t', 't.process_id = product_defect.id AND t.progress_type = ' . Tracking::TYPE_PRODUCT_REPACKAGING)
                ->where(['product_defect.deleted_at' => null])
                ->andWhere(['product_defect.is_repackaging' => true])
                ->andWhere(['DATE(product_defect.created_at)' => $date])
                ->orderBy(['product_defect.created_at' => SORT_DESC]);

            $repackagingProducts = $query->asArray()->all();

            return ApiResponse::response('Продукция для переупаковки', [
                'repackaging_products' => $repackagingProducts
            ]);
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }


    public function actionAcceptRepackagingProduct()
    {
        if (Yii::$app->request->isPost) {
            $product_defect_id = Yii::$app->request->post('product_defect_id');

            if (!$product_defect_id) {
                return ApiResponse::response(
                    'ID записи обязателен',
                    null,
                    ApiResponse::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                $productDefect = ProductDefect::findOne($product_defect_id);
                if (!$productDefect) {
                    throw new \Exception('Запись не найдена');
                }

                $productDefect->accepted_user_id = Yii::$app->user->id;
                $productDefect->accepted_at = date('Y-m-d H:i:s');

                if (!$productDefect->save()) {
                    throw new \Exception('Ошибка при обновлении записи');
                }

                $transaction->commit();
                return ApiResponse::response('Запись успешно обновлена');

            } catch (\Exception $e) {
                $transaction->rollBack();
                return ApiResponse::response(
                    'Ошибка при обновлении записи',
                    ['error' => $e->getMessage()],
                    ApiResponse::HTTP_INTERNAL_SERVER_ERROR
                );
            }
        }

        return ApiResponse::response(
            'Метод не поддерживается',
            null,
            ApiResponse::HTTP_METHOD_NOT_ALLOWED
        );
    }

}