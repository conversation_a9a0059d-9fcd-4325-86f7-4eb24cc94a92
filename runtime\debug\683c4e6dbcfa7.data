a:14:{s:6:"config";s:4971:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:10:"2.0.52-dev";s:11:"application";a:8:{s:3:"yii";s:10:"2.0.52-dev";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:2:"uz";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:16:{s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/base";s:67:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:62:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:60:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-grid/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:77:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-jui/src";}}s:33:"2amigos/yii2-arrayquery-component";a:3:{s:4:"name";s:33:"2amigos/yii2-arrayquery-component";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@dosamigos/arrayquery";s:75:"D:\OSPanel\domains\silverzavod\vendor/2amigos/yii2-arrayquery-component/src";}}s:17:"yii2mod/yii2-rbac";a:3:{s:4:"name";s:17:"yii2mod/yii2-rbac";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii2mod/rbac";s:55:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-rbac";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.25.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:10:"@yii/faker";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.6.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:23:"3.0.9999999.9999999-dev";s:5:"alias";a:10:{s:10:"@yii/queue";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:71:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:72:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/file";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:76:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:78:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:81:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:68:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.11.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap4/src";}}s:20:"yii2mod/yii2-swagger";a:3:{s:4:"name";s:20:"yii2mod/yii2-swagger";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:16:"@yii2mod/swagger";s:58:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-swagger";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:58066:"a:1:{s:8:"messages";a:53:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.726864;i:4;a:0:{}i:5;i:2612032;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.728836;i:4;a:0:{}i:5;i:2729040;}i:2;a:6:{i:0;s:53:"Bootstrap with app\components\SessionTimeoutComponent";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.729536;i:4;a:0:{}i:5;i:2770248;}i:3;a:6:{i:0;s:22:"Bootstrap with Closure";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.729545;i:4;a:0:{}i:5;i:2770624;}i:4;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.74964;i:4;a:0:{}i:5;i:3915824;}i:5;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.765014;i:4;a:0:{}i:5;i:4726080;}i:6;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.773111;i:4;a:0:{}i:5;i:5115744;}i:7;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.780168;i:4;a:0:{}i:5;i:5580152;}i:8;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.780827;i:4;a:0:{}i:5;i:5607544;}i:20;a:6:{i:0;s:55:"Route requested: 'api/raw-material/delete-raw-material'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.784048;i:4;a:0:{}i:5;i:5781264;}i:21;a:6:{i:0;s:19:"Loading module: api";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.78406;i:4;a:0:{}i:5;i:5782912;}i:22;a:6:{i:0;s:50:"Route to run: api/raw-material/delete-raw-material";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.791495;i:4;a:0:{}i:5;i:6200184;}i:23;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.8144;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7868608;}i:26;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.872935;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8072328;}i:29;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.922122;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8118240;}i:32;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.931141;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8414048;}i:35;a:6:{i:0;s:55:"User '6' logged in from 127.0.0.1. Session not enabled.";i:1;i:4;i:2;s:19:"yii\web\User::login";i:3;d:**********.939072;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8700696;}i:36;a:6:{i:0;s:65:"Rate limit skipped: "user" does not implement RateLimitInterface.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:**********.939112;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8701288;}i:37;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.940942;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8888872;}i:40;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.943949;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8895496;}i:43;a:6:{i:0;s:25:"Checking role: raw_keeper";i:1;i:8;i:2;s:40:"yii\rbac\DbManager::checkAccessRecursive";i:3;d:**********.94682;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8898888;}i:44;a:6:{i:0;s:92:"Running action: app\modules\api\controllers\RawMaterialController::actionDeleteRawMaterial()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.946849;i:4;a:0:{}i:5;i:8898048;}i:45;a:6:{i:0;s:17:"Begin transaction";i:1;i:8;i:2;s:25:"yii\db\Transaction::begin";i:3;d:**********.947692;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:27;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:8968136;}i:46;a:6:{i:0;s:127:"SELECT * FROM "invoice" WHERE ("id"='43') AND ("accepted_at" IS NULL) AND ("accept_user_id" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.9489;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:36;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9082232;}i:49;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.950856;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:36;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9094288;}i:52;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.956318;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:36;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9105648;}i:55;a:6:{i:0;s:83:"SELECT * FROM "invoice_detail" WHERE ("invoice_id"='43') AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.96135;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:49;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9136328;}i:58;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.963595;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:49;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9148624;}i:61;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.968394;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:49;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9159536;}i:64;a:6:{i:0;s:126:"SELECT * FROM "material_storage" WHERE ("material_id"=4) AND ("quantity" > 0) AND ("deleted_at" IS NULL) ORDER BY "created_at"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.972442;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:112;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9201936;}i:67;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.974632;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:112;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9222216;}i:70;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.979246;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:112;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9232576;}i:73;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.984548;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:139;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9384496;}i:76;a:6:{i:0;s:97:"UPDATE "material_storage" SET "quantity"='1035', "updated_at"='2025-06-01 17:58:21' WHERE "id"=20";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.986561;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:139;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9384768;}i:79;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.990366;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:167;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9426288;}i:82;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.996424;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:167;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9438344;}i:85;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.999836;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9460296;}i:88;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=20)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.000662;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9466824;}i:91;a:6:{i:0;s:76:"SELECT EXISTS(SELECT * FROM "invoice_detail" WHERE "invoice_detail"."id"=49)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.001342;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9473384;}i:94;a:6:{i:0;s:215:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "created_at", "add_user_id", "type", "invoice_detail_id") VALUES (20, 4, '5', '2025-06-01 17:58:21', 6, 2, 49) RETURNING "id"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.001972;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9477912;}i:97;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=43)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.004382;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:64;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9478376;}i:100;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.004933;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:64;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9483784;}i:103;a:6:{i:0;s:76:"UPDATE "invoice_detail" SET "deleted_at"='2025-06-01 17:58:22' WHERE "id"=49";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.005555;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:64;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9481088;}i:106;a:6:{i:0;s:129:"SELECT * FROM "tracking" WHERE ("process_id"='43') AND ("progress_type"=8) AND ("deleted_at" IS NULL) AND ("accepted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.007343;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:193;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9510872;}i:109;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.009315;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:193;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9523032;}i:112;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.014191;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:193;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9533832;}i:115;a:6:{i:0;s:71:"UPDATE "tracking" SET "deleted_at"='2025-06-01 17:58:22' WHERE "id"=281";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.017852;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:200;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9547232;}i:118;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.019893;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:74;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9626056;}i:121;a:6:{i:0;s:69:"UPDATE "invoice" SET "deleted_at"='2025-06-01 17:58:22' WHERE "id"=43";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.022867;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:74;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9622416;}i:124;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.024095;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:221;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:79;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9637288;}i:127;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.03031;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:221;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:79;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9648080;}i:130;a:6:{i:0;s:353:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (6, 'delete_raw_material', 'invoice', '"43"'::jsonb, '"{\"invoice_number\":\"20250601-2C5EE1EA\",\"supplier_id\":1,\"total_amount\":\"0.00\",\"materials\":[{\"material_id\":4,\"quantity\":5,\"price\":null}]}"'::jsonb, '2025-06-01 17:58:22')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.034546;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:221;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:79;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9706408;}i:133;a:6:{i:0;s:18:"Commit transaction";i:1;i:8;i:2;s:26:"yii\db\Transaction::commit";i:3;d:1748782702.036765;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:81;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9704936;}}}";s:9:"profiling";s:107200:"a:3:{s:6:"memory";i:9898144;s:4:"time";d:0.3296830654144287;s:8:"messages";a:70:{i:24;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.814424;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7870112;}i:25;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.8697;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7872416;}i:27;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.873063;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8074280;}i:28;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.920661;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8090016;}i:30;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.92216;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8120104;}i:31;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.925945;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8122040;}i:33;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.931187;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8418312;}i:34;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.935933;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8421072;}i:38;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.940969;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8891480;}i:39;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.943457;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8893648;}i:41;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.943975;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8898104;}i:42;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.946382;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8900208;}i:47;a:6:{i:0;s:127:"SELECT * FROM "invoice" WHERE ("id"='43') AND ("accepted_at" IS NULL) AND ("accept_user_id" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.948932;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:36;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9085296;}i:48;a:6:{i:0;s:127:"SELECT * FROM "invoice" WHERE ("id"='43') AND ("accepted_at" IS NULL) AND ("accept_user_id" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.95065;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:36;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9088408;}i:50;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.950888;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:36;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9096152;}i:51;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.955502;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:36;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9115160;}i:53;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.956349;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:36;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9107512;}i:54;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.959859;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:36;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9110904;}i:56;a:6:{i:0;s:83:"SELECT * FROM "invoice_detail" WHERE ("invoice_id"='43') AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.961381;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:49;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9139344;}i:57;a:6:{i:0;s:83:"SELECT * FROM "invoice_detail" WHERE ("invoice_id"='43') AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.963303;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:49;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9142624;}i:59;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.963631;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:49;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9150488;}i:60;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.967956;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:49;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9168024;}i:62;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.968418;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:49;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9161400;}i:63;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.971055;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:49;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9165376;}i:65;a:6:{i:0;s:126:"SELECT * FROM "material_storage" WHERE ("material_id"=4) AND ("quantity" > 0) AND ("deleted_at" IS NULL) ORDER BY "created_at"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.972469;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:112;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9211064;}i:66;a:6:{i:0;s:126:"SELECT * FROM "material_storage" WHERE ("material_id"=4) AND ("quantity" > 0) AND ("deleted_at" IS NULL) ORDER BY "created_at"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.974361;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:112;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9216344;}i:68;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.974665;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:112;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9224456;}i:69;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.978827;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:112;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9237568;}i:71;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.979271;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:112;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9234816;}i:72;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.981893;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:112;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9238024;}i:74;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.984574;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:139;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9387872;}i:75;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.986224;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:139;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9389856;}i:77;a:6:{i:0;s:97:"UPDATE "material_storage" SET "quantity"='1035', "updated_at"='2025-06-01 17:58:21' WHERE "id"=20";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.986586;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:139;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9387568;}i:78;a:6:{i:0;s:97:"UPDATE "material_storage" SET "quantity"='1035', "updated_at"='2025-06-01 17:58:21' WHERE "id"=20";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.989207;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:139;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9389616;}i:80;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.990415;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:167;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9428528;}i:81;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.995915;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:167;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9446560;}i:83;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.996455;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:167;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9440584;}i:84;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.999153;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:167;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9445600;}i:86;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.999864;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9463672;}i:87;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.000316;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9465656;}i:89;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=20)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.000686;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9470216;}i:90;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=20)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.001094;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9472232;}i:92;a:6:{i:0;s:76:"SELECT EXISTS(SELECT * FROM "invoice_detail" WHERE "invoice_detail"."id"=49)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.001364;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9476776;}i:93;a:6:{i:0;s:76:"SELECT EXISTS(SELECT * FROM "invoice_detail" WHERE "invoice_detail"."id"=49)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.001712;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9478792;}i:95;a:6:{i:0;s:215:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "created_at", "add_user_id", "type", "invoice_detail_id") VALUES (20, 4, '5', '2025-06-01 17:58:21', 6, 2, 49) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.001983;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9479416;}i:96;a:6:{i:0;s:215:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "created_at", "add_user_id", "type", "invoice_detail_id") VALUES (20, 4, '5', '2025-06-01 17:58:21', 6, 2, 49) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.003751;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9482080;}i:98;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=43)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.004407;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:64;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9481376;}i:99;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=43)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.004743;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:64;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9483016;}i:101;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.004952;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:64;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9486784;}i:102;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.005292;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:64;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9488392;}i:104;a:6:{i:0;s:76:"UPDATE "invoice_detail" SET "deleted_at"='2025-06-01 17:58:22' WHERE "id"=49";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.00557;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:64;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9484112;}i:105;a:6:{i:0;s:76:"UPDATE "invoice_detail" SET "deleted_at"='2025-06-01 17:58:22' WHERE "id"=49";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.006727;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:64;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9485744;}i:107;a:6:{i:0;s:129:"SELECT * FROM "tracking" WHERE ("process_id"='43') AND ("progress_type"=8) AND ("deleted_at" IS NULL) AND ("accepted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.007366;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:193;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9514368;}i:108;a:6:{i:0;s:129:"SELECT * FROM "tracking" WHERE ("process_id"='43') AND ("progress_type"=8) AND ("deleted_at" IS NULL) AND ("accepted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.009062;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:193;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9517240;}i:110;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.009346;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:193;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9525272;}i:111;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.013726;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:193;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9539824;}i:113;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.014212;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:193;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9536072;}i:114;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.017312;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:193;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9538384;}i:116;a:6:{i:0;s:71:"UPDATE "tracking" SET "deleted_at"='2025-06-01 17:58:22' WHERE "id"=281";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.017875;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:200;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9549992;}i:117;a:6:{i:0;s:71:"UPDATE "tracking" SET "deleted_at"='2025-06-01 17:58:22' WHERE "id"=281";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.018407;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:200;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9552000;}i:119;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.019922;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:74;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9629056;}i:120;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.022403;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:74;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9630664;}i:122;a:6:{i:0;s:69:"UPDATE "invoice" SET "deleted_at"='2025-06-01 17:58:22' WHERE "id"=43";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.022905;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:74;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9625440;}i:123;a:6:{i:0;s:69:"UPDATE "invoice" SET "deleted_at"='2025-06-01 17:58:22' WHERE "id"=43";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.023379;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:74;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9627072;}i:125;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.024145;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:221;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:79;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9639528;}i:126;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.029792;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:221;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:79;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9654008;}i:128;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.030338;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:221;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:79;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9650320;}i:129;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.033099;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:221;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:79;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9657600;}i:131;a:6:{i:0;s:353:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (6, 'delete_raw_material', 'invoice', '"43"'::jsonb, '"{\"invoice_number\":\"20250601-2C5EE1EA\",\"supplier_id\":1,\"total_amount\":\"0.00\",\"materials\":[{\"material_id\":4,\"quantity\":5,\"price\":null}]}"'::jsonb, '2025-06-01 17:58:22')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.034573;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:221;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:79;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9710032;}i:132;a:6:{i:0;s:353:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (6, 'delete_raw_material', 'invoice', '"43"'::jsonb, '"{\"invoice_number\":\"20250601-2C5EE1EA\",\"supplier_id\":1,\"total_amount\":\"0.00\",\"materials\":[{\"material_id\":4,\"quantity\":5,\"price\":null}]}"'::jsonb, '2025-06-01 17:58:22')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.036567;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:221;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:79;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9712168;}}}";s:2:"db";s:105955:"a:1:{s:8:"messages";a:68:{i:27;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.873063;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8074280;}i:28;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.920661;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8090016;}i:30;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.92216;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8120104;}i:31;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.925945;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8122040;}i:33;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.931187;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8418312;}i:34;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.935933;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8421072;}i:38;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.940969;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8891480;}i:39;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='6'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.943457;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8893648;}i:41;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.943975;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8898104;}i:42;a:6:{i:0;s:51:"SELECT * FROM "auth_item" WHERE "name"='raw_keeper'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.946382;i:4;a:1:{i:0;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8900208;}i:47;a:6:{i:0;s:127:"SELECT * FROM "invoice" WHERE ("id"='43') AND ("accepted_at" IS NULL) AND ("accept_user_id" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.948932;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:36;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9085296;}i:48;a:6:{i:0;s:127:"SELECT * FROM "invoice" WHERE ("id"='43') AND ("accepted_at" IS NULL) AND ("accept_user_id" IS NULL) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.95065;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:36;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9088408;}i:50;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.950888;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:36;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9096152;}i:51;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.955502;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:36;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9115160;}i:53;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.956349;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:36;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9107512;}i:54;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.959859;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:36;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9110904;}i:56;a:6:{i:0;s:83:"SELECT * FROM "invoice_detail" WHERE ("invoice_id"='43') AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.961381;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:49;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9139344;}i:57;a:6:{i:0;s:83:"SELECT * FROM "invoice_detail" WHERE ("invoice_id"='43') AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.963303;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:49;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9142624;}i:59;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.963631;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:49;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9150488;}i:60;a:6:{i:0;s:2820:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'invoice_detail'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.967956;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:49;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9168024;}i:62;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.968418;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:49;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9161400;}i:63;a:6:{i:0;s:882:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='invoice_detail'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.971055;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:49;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9165376;}i:65;a:6:{i:0;s:126:"SELECT * FROM "material_storage" WHERE ("material_id"=4) AND ("quantity" > 0) AND ("deleted_at" IS NULL) ORDER BY "created_at"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.972469;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:112;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9211064;}i:66;a:6:{i:0;s:126:"SELECT * FROM "material_storage" WHERE ("material_id"=4) AND ("quantity" > 0) AND ("deleted_at" IS NULL) ORDER BY "created_at"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.974361;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:112;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9216344;}i:68;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.974665;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:112;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9224456;}i:69;a:6:{i:0;s:2822:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.978827;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:112;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9237568;}i:71;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.979271;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:112;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9234816;}i:72;a:6:{i:0;s:884:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.981893;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:112;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9238024;}i:74;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.984574;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:139;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9387872;}i:75;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.986224;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:139;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9389856;}i:77;a:6:{i:0;s:97:"UPDATE "material_storage" SET "quantity"='1035', "updated_at"='2025-06-01 17:58:21' WHERE "id"=20";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.986586;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:139;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9387568;}i:78;a:6:{i:0;s:97:"UPDATE "material_storage" SET "quantity"='1035', "updated_at"='2025-06-01 17:58:21' WHERE "id"=20";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.989207;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:139;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9389616;}i:80;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.990415;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:167;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9428528;}i:81;a:6:{i:0;s:2830:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_storage_history'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.995915;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:167;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9446560;}i:83;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.996455;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:167;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9440584;}i:84;a:6:{i:0;s:892:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_storage_history'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.999153;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:167;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9445600;}i:86;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.999864;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9463672;}i:87;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.000316;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9465656;}i:89;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=20)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.000686;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9470216;}i:90;a:6:{i:0;s:80:"SELECT EXISTS(SELECT * FROM "material_storage" WHERE "material_storage"."id"=20)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.001094;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9472232;}i:92;a:6:{i:0;s:76:"SELECT EXISTS(SELECT * FROM "invoice_detail" WHERE "invoice_detail"."id"=49)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.001364;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9476776;}i:93;a:6:{i:0;s:76:"SELECT EXISTS(SELECT * FROM "invoice_detail" WHERE "invoice_detail"."id"=49)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.001712;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9478792;}i:95;a:6:{i:0;s:215:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "created_at", "add_user_id", "type", "invoice_detail_id") VALUES (20, 4, '5', '2025-06-01 17:58:21', 6, 2, 49) RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.001983;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9479416;}i:96;a:6:{i:0;s:215:"INSERT INTO "material_storage_history" ("material_storage_id", "material_id", "quantity", "created_at", "add_user_id", "type", "invoice_detail_id") VALUES (20, 4, '5', '2025-06-01 17:58:21', 6, 2, 49) RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.003751;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:175;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:144;s:8:"function";s:20:"createStorageHistory";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:60;s:8:"function";s:25:"deductMaterialFromStorage";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9482080;}i:98;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=43)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.004407;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:64;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9481376;}i:99;a:6:{i:0;s:62:"SELECT EXISTS(SELECT * FROM "invoice" WHERE "invoice"."id"=43)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.004743;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:64;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9483016;}i:101;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.004952;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:64;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9486784;}i:102;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=4)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.005292;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:64;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9488392;}i:104;a:6:{i:0;s:76:"UPDATE "invoice_detail" SET "deleted_at"='2025-06-01 17:58:22' WHERE "id"=49";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.00557;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:64;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9484112;}i:105;a:6:{i:0;s:76:"UPDATE "invoice_detail" SET "deleted_at"='2025-06-01 17:58:22' WHERE "id"=49";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.006727;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:64;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9485744;}i:107;a:6:{i:0;s:129:"SELECT * FROM "tracking" WHERE ("process_id"='43') AND ("progress_type"=8) AND ("deleted_at" IS NULL) AND ("accepted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.007366;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:193;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9514368;}i:108;a:6:{i:0;s:129:"SELECT * FROM "tracking" WHERE ("process_id"='43') AND ("progress_type"=8) AND ("deleted_at" IS NULL) AND ("accepted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.009062;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:193;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9517240;}i:110;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.009346;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:193;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9525272;}i:111;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.013726;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:193;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9539824;}i:113;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.014212;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:193;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9536072;}i:114;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.017312;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:193;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9538384;}i:116;a:6:{i:0;s:71:"UPDATE "tracking" SET "deleted_at"='2025-06-01 17:58:22' WHERE "id"=281";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.017875;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:200;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9549992;}i:117;a:6:{i:0;s:71:"UPDATE "tracking" SET "deleted_at"='2025-06-01 17:58:22' WHERE "id"=281";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.018407;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:200;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:70;s:8:"function";s:14:"deleteTracking";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9552000;}i:119;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.019922;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:74;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9629056;}i:120;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "supplier" WHERE "supplier"."id"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.022403;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:74;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9630664;}i:122;a:6:{i:0;s:69:"UPDATE "invoice" SET "deleted_at"='2025-06-01 17:58:22' WHERE "id"=43";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.022905;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:74;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9625440;}i:123;a:6:{i:0;s:69:"UPDATE "invoice" SET "deleted_at"='2025-06-01 17:58:22' WHERE "id"=43";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.023379;i:4;a:2:{i:0;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:74;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:80:"D:\OSPanel\domains\silverzavod\modules\api\controllers\RawMaterialController.php";s:4:"line";i:421;s:8:"function";s:20:"deleteMaterialIncome";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9627072;}i:125;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.024145;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:221;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:79;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9639528;}i:126;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.029792;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:221;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:79;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9654008;}i:128;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.030338;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:221;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:79;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9650320;}i:129;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748782702.033099;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:221;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:79;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9657600;}i:131;a:6:{i:0;s:353:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (6, 'delete_raw_material', 'invoice', '"43"'::jsonb, '"{\"invoice_number\":\"20250601-2C5EE1EA\",\"supplier_id\":1,\"total_amount\":\"0.00\",\"materials\":[{\"material_id\":4,\"quantity\":5,\"price\":null}]}"'::jsonb, '2025-06-01 17:58:22')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.034573;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:221;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:79;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9710032;}i:132;a:6:{i:0;s:353:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (6, 'delete_raw_material', 'invoice', '"43"'::jsonb, '"{\"invoice_number\":\"20250601-2C5EE1EA\",\"supplier_id\":1,\"total_amount\":\"0.00\",\"materials\":[{\"material_id\":4,\"quantity\":5,\"price\":null}]}"'::jsonb, '2025-06-01 17:58:22')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748782702.036567;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:221;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:95:"D:\OSPanel\domains\silverzavod\modules\api\services\rawMaterial\MaterialIncomeDeleteService.php";s:4:"line";i:79;s:8:"function";s:9:"logAction";s:5:"class";s:64:"app\modules\api\services\rawMaterial\MaterialIncomeDeleteService";s:4:"type";s:2:"->";}}i:5;i:9712168;}}}";s:5:"event";s:14784:"a:81:{i:0;a:5:{s:4:"time";d:**********.783303;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:**********.791742;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:**********.791768;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\api\ApiModule";}i:3;a:5:{s:4:"time";d:**********.808157;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:4;a:5:{s:4:"time";d:**********.869677;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:5;a:5:{s:4:"time";d:**********.936387;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\modules\api\models\Users";}i:6;a:5:{s:4:"time";d:**********.936438;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\modules\api\models\Users";}i:7;a:5:{s:4:"time";d:**********.936677;s:4:"name";s:11:"beforeLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:8;a:5:{s:4:"time";d:**********.938948;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:9;a:5:{s:4:"time";d:**********.938968;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:10;a:5:{s:4:"time";d:**********.938977;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:11;a:5:{s:4:"time";d:**********.938983;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:12;a:5:{s:4:"time";d:**********.938989;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:13;a:5:{s:4:"time";d:**********.938995;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:14;a:5:{s:4:"time";d:**********.939;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:15;a:5:{s:4:"time";d:**********.939092;s:4:"name";s:10:"afterLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:16;a:5:{s:4:"time";d:**********.93914;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:49:"app\modules\api\controllers\RawMaterialController";}i:17;a:5:{s:4:"time";d:**********.947718;s:4:"name";s:16:"beginTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:18;a:5:{s:4:"time";d:**********.948455;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:19;a:5:{s:4:"time";d:**********.950791;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:20;a:5:{s:4:"time";d:**********.960836;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:21;a:5:{s:4:"time";d:**********.961222;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:22;a:5:{s:4:"time";d:**********.963521;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:23;a:5:{s:4:"time";d:**********.971538;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:24;a:5:{s:4:"time";d:**********.971946;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:25;a:5:{s:4:"time";d:**********.974569;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:26;a:5:{s:4:"time";d:**********.982183;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:27;a:5:{s:4:"time";d:**********.982202;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:28;a:5:{s:4:"time";d:**********.982212;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:29;a:5:{s:4:"time";d:**********.982221;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:30;a:5:{s:4:"time";d:**********.982232;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:31;a:5:{s:4:"time";d:**********.982236;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:32;a:5:{s:4:"time";d:**********.982239;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:33;a:5:{s:4:"time";d:**********.982243;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:34;a:5:{s:4:"time";d:**********.982246;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:35;a:5:{s:4:"time";d:**********.982529;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:36;a:5:{s:4:"time";d:**********.984419;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:37;a:5:{s:4:"time";d:**********.984454;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:38;a:5:{s:4:"time";d:**********.986428;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:39;a:5:{s:4:"time";d:**********.986446;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:40;a:5:{s:4:"time";d:**********.989734;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\common\models\MaterialStorage";}i:41;a:5:{s:4:"time";d:**********.990258;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:42;a:5:{s:4:"time";d:**********.999492;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:43;a:5:{s:4:"time";d:**********.999707;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:44;a:5:{s:4:"time";d:**********.999737;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:45;a:5:{s:4:"time";d:1748782702.000546;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:46;a:5:{s:4:"time";d:1748782702.00058;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:47;a:5:{s:4:"time";d:1748782702.001234;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:48;a:5:{s:4:"time";d:1748782702.001264;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:49;a:5:{s:4:"time";d:1748782702.001834;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:50;a:5:{s:4:"time";d:1748782702.001849;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:51;a:5:{s:4:"time";d:1748782702.004074;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"app\common\models\MaterialStorageHistory";}i:52;a:5:{s:4:"time";d:1748782702.004112;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:53;a:5:{s:4:"time";d:1748782702.004291;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:54;a:5:{s:4:"time";d:1748782702.004316;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:55;a:5:{s:4:"time";d:1748782702.004855;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:56;a:5:{s:4:"time";d:1748782702.004877;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:57;a:5:{s:4:"time";d:1748782702.005479;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:58;a:5:{s:4:"time";d:1748782702.005491;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:59;a:5:{s:4:"time";d:1748782702.006914;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"app\common\models\InvoiceDetail";}i:60;a:5:{s:4:"time";d:1748782702.007245;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:61;a:5:{s:4:"time";d:1748782702.009259;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:62;a:5:{s:4:"time";d:1748782702.017604;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:63;a:5:{s:4:"time";d:1748782702.017635;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:64;a:5:{s:4:"time";d:1748782702.017769;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:65;a:5:{s:4:"time";d:1748782702.017779;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:66;a:5:{s:4:"time";d:1748782702.018545;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:67;a:5:{s:4:"time";d:1748782702.018582;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:68;a:5:{s:4:"time";d:1748782702.019766;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:69;a:5:{s:4:"time";d:1748782702.019803;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:70;a:5:{s:4:"time";d:1748782702.022701;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:71;a:5:{s:4:"time";d:1748782702.022726;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:72;a:5:{s:4:"time";d:1748782702.023507;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\common\models\Invoice";}i:73;a:5:{s:4:"time";d:1748782702.038006;s:4:"name";s:17:"commitTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:74;a:5:{s:4:"time";d:1748782702.038418;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:49:"app\modules\api\controllers\RawMaterialController";}i:75;a:5:{s:4:"time";d:1748782702.03903;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\api\ApiModule";}i:76;a:5:{s:4:"time";d:1748782702.039048;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:77;a:5:{s:4:"time";d:1748782702.039064;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:78;a:5:{s:4:"time";d:1748782702.039076;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:79;a:5:{s:4:"time";d:1748782702.040855;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:80;a:5:{s:4:"time";d:1748782702.040964;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:**********.714946;s:3:"end";d:1748782702.045736;s:6:"memory";i:9991440;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:2240:"a:3:{s:8:"messages";a:11:{i:9;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.783939;i:4;a:0:{}i:5;i:5773368;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.78396;i:4;a:0:{}i:5;i:5774120;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.783969;i:4;a:0:{}i:5;i:5774872;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.783977;i:4;a:0:{}i:5;i:5775624;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.783984;i:4;a:0:{}i:5;i:5776376;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.78399;i:4;a:0:{}i:5;i:5777128;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:5:"login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.783996;i:4;a:0:{}i:5;i:5777880;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:6:"logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.784002;i:4;a:0:{}i:5;i:5778632;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:13:"site/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.784009;i:4;a:0:{}i:5;i:5780024;}i:18;a:6:{i:0;s:69:"Request parsed with URL rule: api/<controller:[\w-]+>/<action:[\w-]+>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:**********.784031;i:4;a:0:{}i:5;i:5782120;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:39:"api/<controller:[\w-]+>/<action:[\w-]+>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.784038;i:4;a:0:{}i:5;i:5781936;}}s:5:"route";s:36:"api/raw-material/delete-raw-material";s:6:"action";s:76:"app\modules\api\controllers\RawMaterialController::actionDeleteRawMaterial()";}";s:7:"request";s:4387:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:11:{s:13:"authorization";s:47:"Bearer uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-";s:10:"user-agent";s:21:"PostmanRuntime/7.44.0";s:6:"accept";s:3:"*/*";s:13:"cache-control";s:8:"no-cache";s:13:"postman-token";s:36:"7052158c-b797-40d1-aa16-76d807ee20c7";s:4:"host";s:6:"silver";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:10:"connection";s:10:"keep-alive";s:12:"content-type";s:80:"multipart/form-data; boundary=--------------------------664602281777382187312035";s:6:"cookie";s:216:"PHPSESSID=ijocj4ons9v73rb97jao7352v8c6b4cn; _csrf=66c2243f4636d3afd9a4920df6113fb93496286dc5781f90cd310b6f98b18292a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22LzJnB5fg2Ka-UP93jS7hoHvlqU1Ky8y5%22%3B%7D";s:14:"content-length";s:3:"167";}s:15:"responseHeaders";a:9:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:4:"Vary";s:6:"Accept";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"683c4e6dbcfa7";s:16:"X-Debug-Duration";s:3:"327";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=683c4e6dbcfa7";s:10:"Set-Cookie";s:204:"_csrf=7d05f4fa9ee7ba959c6ba95bd8f2fa2b37c6181366bded73c6d3ffd53fa4cf12a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22DJq8m5Sw3t5vOLz6rMC09-k8vWXQn7l6%22%3B%7D; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:36:"api/raw-material/delete-raw-material";s:6:"action";s:76:"app\modules\api\controllers\RawMaterialController::actionDeleteRawMaterial()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:39:{s:15:"REDIRECT_STATUS";s:3:"200";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.0";s:11:"HTTP_ACCEPT";s:3:"*/*";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:18:"HTTP_POSTMAN_TOKEN";s:36:"7052158c-b797-40d1-aa16-76d807ee20c7";s:9:"HTTP_HOST";s:6:"silver";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:12:"CONTENT_TYPE";s:80:"multipart/form-data; boundary=--------------------------664602281777382187312035";s:11:"HTTP_COOKIE";s:216:"PHPSESSID=ijocj4ons9v73rb97jao7352v8c6b4cn; _csrf=66c2243f4636d3afd9a4920df6113fb93496286dc5781f90cd310b6f98b18292a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22LzJnB5fg2Ka-UP93jS7hoHvlqU1Ky8y5%22%3B%7D";s:14:"CONTENT_LENGTH";s:3:"167";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:6:"silver";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:44:"D:/OSPanel/domains/silverzavod/web/index.php";s:11:"REMOTE_PORT";s:5:"57578";s:12:"REDIRECT_URL";s:37:"/api/raw-material/delete-raw-material";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:37:"/api/raw-material/delete-raw-material";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.700752;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:0:{}s:4:"POST";a:1:{s:10:"invoice_id";s:2:"43";}s:6:"COOKIE";a:2:{s:9:"PHPSESSID";s:32:"ijocj4ons9v73rb97jao7352v8c6b4cn";s:5:"_csrf";s:130:"66c2243f4636d3afd9a4920df6113fb93496286dc5781f90cd310b6f98b18292a:2:{i:0;s:5:"_csrf";i:1;s:32:"LzJnB5fg2Ka-UP93jS7hoHvlqU1Ky8y5";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:1:{s:7:"__flash";a:0:{}}}";s:4:"user";s:2139:"a:5:{s:2:"id";i:6;s:8:"identity";a:8:{s:2:"id";s:1:"6";s:8:"username";s:12:"'raw_keeper'";s:9:"full_name";s:20:"'Hom ashyo ishchisi'";s:4:"role";s:1:"0";s:12:"access_token";s:42:"'uFWtUzUzaB-Zp42OVB4m4aE_-5UkNDhp89Zmvyx-'";s:8:"password";s:62:"'$2y$13$bDj/q/7SKFwJIrGKBX7Z8O5TvDtnhjH0Q9YZKxl858G.7vVP8/a.m'";s:10:"created_at";s:21:"'2025-03-16 16:22:43'";s:10:"deleted_at";s:4:"null";}s:10:"attributes";a:8:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:10:"Логин";}i:2;a:2:{s:9:"attribute";s:9:"full_name";s:5:"label";s:9:"Full Name";}i:3;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:4;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}i:5;a:2:{s:9:"attribute";s:8:"password";s:5:"label";s:10:"Парол";}i:6;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:7;a:2:{s:9:"attribute";s:10:"deleted_at";s:5:"label";s:10:"Deleted At";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";N;s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:1:{s:10:"raw_keeper";a:7:{s:4:"type";i:1;s:4:"name";s:10:"raw_keeper";s:11:"description";s:10:"Raw keeper";s:8:"ruleName";N;s:4:"data";s:4:"null";s:9:"createdAt";i:**********;s:9:"updatedAt";i:**********;}}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"683c4e6dbcfa7";s:3:"url";s:50:"http://silver/api/raw-material/delete-raw-material";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:**********.700752;s:10:"statusCode";i:200;s:8:"sqlCount";i:34;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9898144;s:14:"processingTime";d:0.3296830654144287;}s:10:"exceptions";a:0:{}}